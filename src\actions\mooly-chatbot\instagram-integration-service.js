/**
 * Instagram Business API Integration Service
 * Provides auto reply for Instagram comments and direct messages
 * with optimized token refresh and AI-powered responses
 */

import { fetchData, updateData, createData, deleteData } from './supabase-utils.js';
import { CONFIG } from 'src/global-config.js';

// Constants
const INSTAGRAM_API_BASE_URL = 'https://graph.facebook.com';
const INSTAGRAM_API_VERSION = CONFIG.instagram.apiVersion;
const INSTAGRAM_APP_ID = CONFIG.instagram.appId;
const INSTAGRAM_APP_SECRET = CONFIG.instagram.appSecret;
const INSTAGRAM_REDIRECT_URI = CONFIG.instagram.redirectUri || `${CONFIG.socialMedia.webhookBaseUrl}/api/instagram-integration/callback`;

// Tables - Instagram has its own separate tables
const INSTAGRAM_ACCOUNTS_TABLE = 'instagram_accounts';
const INSTAGRAM_CONFIG_TABLE = 'instagram_auto_reply_config';
const INSTAGRAM_LOGS_TABLE = 'instagram_activity_logs';
const WEBHOOKS_TABLE = 'social_media_webhooks';
const INTERACTIONS_TABLE = 'social_media_interactions';

/**
 * Instagram Account Management
 */

/**
 * Get Instagram Business accounts for tenant
 * @param {string} tenantId - Tenant ID
 * @returns {Promise<Array>} Instagram accounts
 */
export async function getInstagramAccounts(tenantId) {
    try {
        const { data, error } = await fetchData(INSTAGRAM_ACCOUNTS_TABLE, {
            tenant_id: tenantId,
            is_active: true
        });

        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error('❌ Error fetching Instagram accounts:', error);
        throw error;
    }
}

/**
 * Get Instagram account by ID
 * @param {string} accountId - Instagram Business Account ID
 * @param {string} tenantId - Tenant ID
 * @returns {Promise<Object|null>} Instagram account
 */
export async function getInstagramAccountById(accountId, tenantId) {
    try {
        const { data, error } = await fetchData(INSTAGRAM_ACCOUNTS_TABLE, {
            account_id: accountId,
            tenant_id: tenantId
        });

        if (error) throw error;
        return data?.[0] || null;
    } catch (error) {
        console.error('❌ Error fetching Instagram account:', error);
        throw error;
    }
}

/**
 * Save or update Instagram account
 * @param {Object} accountData - Instagram account data
 * @returns {Promise<Object>} Saved account
 */
export async function upsertInstagramAccount(accountData) {
    try {
        const existingAccount = await getInstagramAccountById(
            accountData.account_id, 
            accountData.tenant_id
        );

        const accountPayload = {
            ...accountData,
            updated_at: new Date().toISOString()
        };

        if (existingAccount) {
            const { data, error } = await updateData(
                INSTAGRAM_ACCOUNTS_TABLE,
                { id: existingAccount.id },
                accountPayload
            );
            if (error) throw error;
            return data;
        } else {
            const { data, error } = await createData(INSTAGRAM_ACCOUNTS_TABLE, {
                ...accountPayload,
                created_at: new Date().toISOString()
            });
            if (error) throw error;
            return data;
        }
    } catch (error) {
        console.error('❌ Error upserting Instagram account:', error);
        throw error;
    }
}

/**
 * Instagram API Functions
 */

/**
 * Get Instagram Business Account info
 * @param {string} accessToken - Access token
 * @param {string} instagramBusinessAccountId - Instagram Business Account ID
 * @returns {Promise<Object>} Account info
 */
export async function getInstagramBusinessAccountInfo(accessToken, instagramBusinessAccountId) {
    try {
        const accountUrl = `${INSTAGRAM_API_BASE_URL}/${INSTAGRAM_API_VERSION}/${instagramBusinessAccountId}`;
        const accountParams = new URLSearchParams({
            fields: 'id,username,name,account_type,followers_count,follows_count,media_count,profile_picture_url,biography,website',
            access_token: accessToken
        });

        const response = await fetch(`${accountUrl}?${accountParams}`);
        const accountData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Account API Error: ${accountData.error?.message || 'Unknown error'}`);
        }

        return accountData;
    } catch (error) {
        console.error('❌ Error getting Instagram business account info:', error);
        throw error;
    }
}

/**
 * Get Instagram media
 * @param {string} accessToken - Access token
 * @param {string} instagramBusinessAccountId - Instagram Business Account ID
 * @param {number} limit - Limit of media to fetch
 * @returns {Promise<Object>} Media data
 */
export async function getInstagramMedia(accessToken, instagramBusinessAccountId, limit = 25) {
    try {
        const mediaUrl = `${INSTAGRAM_API_BASE_URL}/${INSTAGRAM_API_VERSION}/${instagramBusinessAccountId}/media`;
        const mediaParams = new URLSearchParams({
            fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,like_count,comments_count',
            limit: limit.toString(),
            access_token: accessToken
        });

        const response = await fetch(`${mediaUrl}?${mediaParams}`);
        const mediaData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Media API Error: ${mediaData.error?.message || 'Unknown error'}`);
        }

        return mediaData;
    } catch (error) {
        console.error('❌ Error getting Instagram media:', error);
        throw error;
    }
}

/**
 * Reply to Instagram comment
 * @param {string} accessToken - Access token
 * @param {string} commentId - Comment ID
 * @param {string} message - Reply message
 * @returns {Promise<Object>} Reply result
 */
export async function replyToInstagramComment(accessToken, commentId, message) {
    try {
        console.log('💬 Replying to Instagram comment...');
        
        const replyUrl = `${INSTAGRAM_API_BASE_URL}/${INSTAGRAM_API_VERSION}/${commentId}/replies`;
        
        const response = await fetch(replyUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                message: message,
                access_token: accessToken
            })
        });

        const replyData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Comment Reply Error: ${replyData.error?.message || 'Unknown error'}`);
        }

        console.log('✅ Instagram comment reply sent successfully');
        return replyData;
    } catch (error) {
        console.error('❌ Error replying to Instagram comment:', error);
        throw error;
    }
}

/**
 * Send Instagram direct message
 * @param {string} accessToken - Access token
 * @param {string} instagramBusinessAccountId - Instagram Business Account ID
 * @param {string} recipientId - Recipient user ID
 * @param {string} message - Message text
 * @returns {Promise<Object>} Message result
 */
export async function sendInstagramDirectMessage(accessToken, instagramBusinessAccountId, recipientId, message) {
    try {
        console.log('💌 Sending Instagram direct message...');
        
        const messageUrl = `${INSTAGRAM_API_BASE_URL}/${INSTAGRAM_API_VERSION}/${instagramBusinessAccountId}/messages`;
        
        const response = await fetch(messageUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                recipient: JSON.stringify({ id: recipientId }),
                message: JSON.stringify({ text: message }),
                access_token: accessToken
            })
        });

        const messageData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Direct Message Error: ${messageData.error?.message || 'Unknown error'}`);
        }

        console.log('✅ Instagram direct message sent successfully');
        return messageData;
    } catch (error) {
        console.error('❌ Error sending Instagram direct message:', error);
        throw error;
    }
}

/**
 * Subscribe to Instagram webhooks
 * @param {string} accessToken - Access token
 * @param {string} instagramBusinessAccountId - Instagram Business Account ID
 * @param {string} callbackUrl - Webhook callback URL
 * @param {Array} fields - Webhook fields to subscribe
 * @returns {Promise<Object>} Subscription result
 */
export async function subscribeInstagramWebhooks(accessToken, instagramBusinessAccountId, callbackUrl, fields = ['comments', 'messages']) {
    try {
        console.log('🔗 Subscribing to Instagram webhooks...');
        
        const subscriptionUrl = `${INSTAGRAM_API_BASE_URL}/${INSTAGRAM_API_VERSION}/${instagramBusinessAccountId}/subscribed_apps`;
        
        const response = await fetch(subscriptionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                subscribed_fields: fields.join(','),
                callback_url: callbackUrl,
                verify_token: 'instagram_webhook_verify',
                access_token: accessToken
            })
        });

        const subscriptionData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Webhook Subscription Error: ${subscriptionData.error?.message || 'Unknown error'}`);
        }

        console.log('✅ Instagram webhook subscription successful');
        return subscriptionData;
    } catch (error) {
        console.error('❌ Error subscribing to Instagram webhooks:', error);
        throw error;
    }
}

/**
 * Log Instagram activity
 * @param {string} tenantId - Tenant ID
 * @param {string} accountId - Instagram account ID
 * @param {string} activity - Activity type
 * @param {Object} metadata - Additional metadata
 * @returns {Promise<Object>} Log result
 */
export async function logInstagramActivity(tenantId, accountId, activity, metadata = {}) {
    try {
        const logData = {
            tenant_id: tenantId,
            account_id: accountId,
            activity: activity,
            metadata: metadata,
            created_at: new Date().toISOString()
        };

        const { data, error } = await createData(INSTAGRAM_LOGS_TABLE, logData);
        
        if (error) throw error;
        return data;
    } catch (error) {
        console.error('❌ Error logging Instagram activity:', error);
        throw error;
    }
}

/**
 * Refresh Instagram access token
 * @param {string} accessToken - Current access token
 * @returns {Promise<Object>} New token data
 */
export async function refreshInstagramAccessToken(accessToken) {
    try {
        console.log('🔄 Refreshing Instagram access token...');
        
        const refreshUrl = `${INSTAGRAM_API_BASE_URL}/oauth/access_token`;
        
        const response = await fetch(refreshUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                grant_type: 'ig_refresh_token',
                access_token: accessToken
            })
        });

        const tokenData = await response.json();

        if (!response.ok) {
            throw new Error(`Instagram Token Refresh Error: ${tokenData.error?.message || 'Unknown error'}`);
        }

        console.log('✅ Instagram access token refreshed successfully');
        return tokenData;
    } catch (error) {
        console.error('❌ Error refreshing Instagram access token:', error);
        throw error;
    }
}