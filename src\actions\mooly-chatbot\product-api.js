import { TABLE_NAME } from './product-constants';
import { fetchData, createData, updateData, deleteData, upsertData } from './supabase-utils';

/**
 * L<PERSON>y danh sách sản phẩm
 * @param {Object} options - <PERSON><PERSON><PERSON> tù<PERSON> chọn
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API
 */
export async function getProducts(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * L<PERSON>y thông tin chi tiết sản phẩm theo ID
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - <PERSON><PERSON><PERSON> quả từ API
 */
export async function getProductById(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return fetchData(TABLE_NAME, {
    filters: { id: productId },
    single: true,
  });
}

/**
 * Kiểm tra SKU có tồn tại hay không
 * @param {string} sku - <PERSON>U cần kiểm tra
 * @param {string} excludeId - ID sản phẩm cần loại trừ (cho trường hợp edit)
 * @returns {Promise<boolean>} - true nếu SKU đã tồn tại
 */
export async function checkSKUExists(sku, excludeId = null) {
  try {
    if (!sku || sku.trim() === '') {
      return false;
    }

    const trimmedSku = sku.trim();
    let query = {
      filters: { sku: trimmedSku },
      limit: 1 // Chỉ cần kiểm tra có tồn tại hay không
    };

    // Nếu có excludeId, loại trừ sản phẩm đó khỏi kết quả
    if (excludeId) {
      query.filters.id = { operator: 'neq', value: excludeId };
    }

    const result = await fetchData(TABLE_NAME, query);
    return result.success && result.data && result.data.length > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Tạo sản phẩm mới (chỉ thông tin cơ bản)
 * @param {Object} productData - Dữ liệu sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createProductBase(productData) {
  try {
    // Đảm bảo có stock_quantity mặc định nếu không được cung cấp
    if (productData.stock_quantity === undefined) {
      productData.stock_quantity = 0;
    }

    // Kiểm tra SKU unique trước khi tạo
    if (productData.sku) {
      const skuExists = await checkSKUExists(productData.sku);
      if (skuExists) {
        return {
          success: false,
          error: { message: 'SKU đã tồn tại. Vui lòng sử dụng SKU khác.' },
          data: null
        };
      }
    }

    const result = await createData(TABLE_NAME, productData);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Cập nhật thông tin cơ bản của sản phẩm
 * @param {string} productId - ID sản phẩm
 * @param {Object} productData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductBase(productId, productData) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  try {
    // Kiểm tra SKU unique trước khi cập nhật (nếu có thay đổi SKU)
    if (productData.sku) {
      const skuExists = await checkSKUExists(productData.sku, productId);
      if (skuExists) {
        return {
          success: false,
          error: { message: 'SKU đã tồn tại. Vui lòng sử dụng SKU khác.' },
          data: null
        };
      }
    }

    // Cập nhật thông tin sản phẩm
    const result = await updateData(TABLE_NAME, productData, { id: productId });
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Xóa sản phẩm
 * @param {string} productId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProductBase(productId) {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return deleteData(TABLE_NAME, { id: productId });
}

/**
 * Tạo hoặc cập nhật sản phẩm
 * @param {Object} productData - Dữ liệu sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertProductBase(productData) {
  try {
    const result = await upsertData(TABLE_NAME, productData);
    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}
