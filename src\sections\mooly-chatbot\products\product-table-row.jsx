'use client';

import { useState } from 'react';
import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Avatar from '@mui/material/Avatar';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { fDateTime } from 'src/utils/format-time';
import { fCurrency } from 'src/utils/format-number';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { Iconify } from 'src/components/iconify';

import InventoryQuickUpdateDialog from './inventory-quick-update-dialog';

// ----------------------------------------------------------------------

// Map loại sản phẩm sang tên hiển thị và màu sắc
const PRODUCT_TYPE_MAP = {
  [PRODUCT_TYPES.SIMPLE]: { label: 'Đơn giản', color: 'primary' },
  [PRODUCT_TYPES.VARIABLE]: { label: 'Biến thể', color: 'info' },
  [PRODUCT_TYPES.DIGITAL]: { label: 'Số hóa', color: 'success' },
  [PRODUCT_TYPES.SERVICE]: { label: 'Dịch vụ', color: 'warning' },
};

export default function ProductTableRow({
  product,
  selected,
  onSelectRow,
  onEdit,
  onDelete,
  onToggleActive,
  onInventoryUpdated,
}) {
  // State cho dialog cập nhật tồn kho
  const [openInventoryDialog, setOpenInventoryDialog] = useState(false);

  // Lấy thông tin sản phẩm
  const { id, name, stockQuantity, type, updatedAt, isActive } = product;

  // Lấy giá từ cấu trúc mới
  const price = product.sellingPrice || product.selling_price || product.price || 0;

  // Lấy thông tin loại sản phẩm
  const productType = PRODUCT_TYPE_MAP[type] || { label: 'Khác', color: 'default' };

  // Xử lý khi toggle trạng thái active
  const handleToggleActive = (event) => {
    event.stopPropagation();
    event.preventDefault(); // Ngăn chặn các event khác

    if (onToggleActive) {
      onToggleActive(id, !isActive);
    }
  };

  // Xử lý mở dialog cập nhật tồn kho
  const handleOpenInventoryDialog = (event) => {
    event.stopPropagation();
    setOpenInventoryDialog(true);
  };

  // Xử lý đóng dialog cập nhật tồn kho
  const handleCloseInventoryDialog = () => {
    setOpenInventoryDialog(false);
  };

  // Xử lý khi cập nhật tồn kho thành công
  const handleInventoryUpdated = () => {
    if (onInventoryUpdated) {
      onInventoryUpdated();
    }
  };

  return (
    <>
      <TableRow hover tabIndex={-1} selected={selected}>
        <TableCell padding="checkbox">
          <Checkbox checked={selected} onClick={onSelectRow} />
        </TableCell>

        <TableCell>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar
              alt={name}
              src={product.avatar || null}
              variant="rounded"
              sx={{ width: 48, height: 48, borderRadius: 1 }}
            />
            <Typography variant="subtitle2" noWrap>
              {name}
            </Typography>
          </Stack>
        </TableCell>

        <TableCell align="left">
          <Typography variant="body2">{fCurrency(price)}</Typography>
        </TableCell>

        <TableCell align="left">
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="body2">{stockQuantity}</Typography>

            <Tooltip title="Cập nhật tồn kho">
              <IconButton
                size="small"
                color="primary"
                onClick={handleOpenInventoryDialog}
                sx={{ ml: 1 }}
              >
                <Iconify icon="eva:edit-2-fill" width={18} />
              </IconButton>
            </Tooltip>
          </Stack>
        </TableCell>

        <TableCell align="left">
          <Chip label={productType.label} color={productType.color} size="small" variant="soft" />
        </TableCell>

        <TableCell align="left">
          <Typography variant="body2">{fDateTime(updatedAt, 'DD-MM-YYYY HH:mm')}</Typography>
        </TableCell>

        <TableCell align="right">
          <Stack direction="row" spacing={1} justifyContent="flex-end" alignItems="center">
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
              }}
            >
              <Switch
                size="small"
                checked={isActive}
                onChange={handleToggleActive}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
                onMouseDown={(e) => e.stopPropagation()}
                color={isActive ? 'success' : 'default'}
                sx={{
                  '& .MuiSwitch-switchBase': {
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                  },
                }}
              />
            </Box>

            <IconButton color="info" onClick={onEdit}>
              <Iconify icon="eva:edit-fill" />
            </IconButton>

            <IconButton color="error" onClick={onDelete}>
              <Iconify icon="eva:trash-2-outline" />
            </IconButton>
          </Stack>
        </TableCell>
      </TableRow>

      {/* Dialog cập nhật tồn kho */}
      {openInventoryDialog && (
        <InventoryQuickUpdateDialog
          open={openInventoryDialog}
          onClose={handleCloseInventoryDialog}
          product={product}
          onSuccess={handleInventoryUpdated}
        />
      )}
    </>
  );
}

ProductTableRow.propTypes = {
  product: PropTypes.object.isRequired,
  selected: PropTypes.bool,
  onSelectRow: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onToggleActive: PropTypes.func,
  onInventoryUpdated: PropTypes.func,
};

ProductTableRow.defaultProps = {
  selected: false,
  onSelectRow: () => {},
  onEdit: () => {},
  onDelete: () => {},
  onToggleActive: () => {},
  onInventoryUpdated: () => {},
};


