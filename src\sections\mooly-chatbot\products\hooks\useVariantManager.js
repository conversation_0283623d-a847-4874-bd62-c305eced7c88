import { useState, useEffect, useCallback } from 'react';
import { updateData } from '../../../../actions/mooly-chatbot/supabase-utils';

/**
 * Custom hook để quản lý biến thể sản phẩm
 * @param {Function} watch - React Hook Form watch function
 * @param {Function} setValue - React Hook Form setValue function
 * @param {Boolean} isEditMode - Xác định xem đang ở chế độ chỉnh sửa hay không
 * @param {Function} trigger - React Hook Form trigger function
 * @returns {Object} - Các hàm và state để quản lý biến thể
 */
export default function useVariantManager(watch, setValue, isEditMode = false, trigger = null) {
  // Sử dụng watch trực tiếp để luôn nhận được giá trị mới nhất

  const variants = watch('variants') || [];

  // L<PERSON>y danh sách hình ảnh trực tiếp từ form để đảm bảo lu<PERSON>n có dữ liệu mới nhất
  const images = watch('images') || [];

  const [bulkEditField, setBulkEditField] = useState('');
  const [bulkEditValue, setBulkEditValue] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedVariantForImage, setSelectedVariantForImage] = useState(null);
  const [imageSelectOpen, setImageSelectOpen] = useState(false);

  // Memoize hàm đệ quy để tạo tổ hợp
  const generateCombinations = useCallback((attrList, index = 0, current = {}) => {
    // Kiểm tra đầu vào
    if (!attrList || !Array.isArray(attrList) || attrList.length === 0) {
      return [];
    }

    // Điều kiện dừng đệ quy
    if (index === attrList.length) {
      return [current];
    }

    const attribute = attrList[index];

    // Kiểm tra thuộc tính hợp lệ
    if (
      !attribute ||
      !attribute.values ||
      !Array.isArray(attribute.values) ||
      attribute.values.length === 0
    ) {
      // Bỏ qua thuộc tính không hợp lệ và tiếp tục với thuộc tính tiếp theo
      return generateCombinations(attrList, index + 1, current);
    }

    const combinations = [];

    // Tạo tổ hợp cho mỗi giá trị của thuộc tính hiện tại
    attribute.values.forEach((value) => {
      const newCurrent = {
        ...current,
        [attribute.name]: value,
      };
      combinations.push(...generateCombinations(attrList, index + 1, newCurrent));
    });

    return combinations;
  }, []);

  // Đảm bảo mỗi biến thể có ID và tên - memoize để tránh re-render không cần thiết
  useEffect(() => {
    // Chỉ xử lý khi có variants nhưng chưa có ID
    if (variants.length > 0 && !variants[0].id) {
      // Tạo một bản sao để tránh thay đổi trực tiếp variants
      const updatedVariants = variants.map((variant, index) => {
        // Nếu variant đã có ID, giữ nguyên
        if (variant.id) return variant;

        // Tạo tên biến thể từ các thuộc tính
        const variantName = Object.entries(variant)
          .filter(
            ([key]) =>
              ![
                'id',
                'sku',
                'price',
                'salePrice',
                'costPrice',
                'stockQuantity',
                'weight',
                'isActive',
                'avatar',
                'attributes',
              ].includes(key)
          )
          .map(([_, value]) => `${value}`)
          .join(' / ');

        return {
          ...variant,
          id: `variant-${index + 1}`,
          name: variantName,
        };
      });

      // Chỉ cập nhật nếu có sự thay đổi
      if (JSON.stringify(updatedVariants) !== JSON.stringify(variants)) {
        setValue('variants', updatedVariants, { shouldDirty: true });
      }
    }
  }, [variants, setValue]);

  /**
   * Tạo tất cả các tổ hợp biến thể có thể có từ các thuộc tính
   * @param {Array} attrs - Danh sách thuộc tính
   */
  /**
   * Tối ưu: Helper function để tạo SKU cho variant
   */
  const generateVariantSKU = useCallback((name) => {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
    const baseSlug = name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, '');

    return `${baseSlug}-${timestamp}-${randomSuffix}`.toUpperCase();
  }, []);

  const generateVariants = useCallback(
    (attrs) => {
      // Nếu đang ở chế độ chỉnh sửa, không tạo lại biến thể
      if (isEditMode) {
        console.log('Đang ở chế độ chỉnh sửa, không tạo lại biến thể');
        return;
      }

      if (!attrs || !attrs.length) {
        setValue('variants', [], { shouldDirty: true });
        return;
      }

      // Gọi hàm generateCombinations
      const combinationResults = generateCombinations(attrs);

      // Kiểm tra xem đã có variants chưa để giữ lại thông tin cũ
      if (variants.length > 0) {
        const newVariants = combinationResults.map((combo) => {
          // Tạo tên biến thể từ tổ hợp
          const variantName = Object.entries(combo)
            .map(([_, value]) => `${value}`)
            .join(' / ');

          // Tìm biến thể cũ có cùng tổ hợp để giữ lại thông tin
          const existingVariant = variants.find((v) =>
            Object.entries(combo).every(([key, value]) => v[key] === value)
          );

          // Tối ưu: Sử dụng helper function để tạo SKU

          // Tạo biến thể mới với dữ liệu từ biến thể cũ nếu có
          return {
            ...combo,
            id: existingVariant?.id || `variant-${Math.random().toString(36).substring(2, 9)}`,
            productId: existingVariant?.productId || null,
            name: variantName,
            sku: existingVariant?.sku || generateVariantSKU(variantName),
            barcode: existingVariant?.barcode || '',
            price: existingVariant?.price || existingVariant?.sellingPrice || 0,
            compareAtPrice: existingVariant?.compareAtPrice || existingVariant?.salePrice || null,
            costPrice: existingVariant?.costPrice || 0,
            stockQuantity: existingVariant?.stockQuantity || 0,
            weight: existingVariant?.weight || null,
            isActive: existingVariant?.isActive !== undefined ? existingVariant.isActive : true,
            avatar: existingVariant?.avatar || null,
            attributes: combo,
          };
        });

        // Đảm bảo cập nhật form với đầy đủ options
        setValue('variants', newVariants, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });
      } else {
        // Nếu chưa có variants, tạo mới hoàn toàn
        const newVariants = combinationResults.map((combo, index) => {
          // Tạo tên biến thể từ tổ hợp
          const variantName = Object.entries(combo)
            .map(([_, value]) => `${value}`)
            .join(' / ');

          // Tối ưu: Sử dụng helper function để tạo SKU cho variant mới

          // Tạo biến thể mới
          return {
            ...combo,
            id: `variant-${index + 1}`,
            productId: null,
            name: variantName,
            sku: generateVariantSKU(variantName),
            barcode: '',
            price: 0,
            compareAtPrice: null,
            costPrice: 0,
            stockQuantity: 0,
            weight: null,
            isActive: true,
            avatar: null,
            attributes: combo,
          };
        });

        // Đảm bảo cập nhật form với đầy đủ options
        setValue('variants', newVariants, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });
      }
    },
    [variants, setValue, generateCombinations, isEditMode, generateVariantSKU]
  );

  /**
   * Cập nhật thông tin của một biến thể
   * @param {string} variantId - ID của biến thể cần cập nhật
   * @param {string} field - Tên trường cần cập nhật
   * @param {any} value - Giá trị mới
   */
  const handleVariantChange = useCallback(
    (variantId, field, value) => {
      // Nếu đang ở chế độ chỉnh sửa và field là stockQuantity, không cho phép cập nhật
      if (isEditMode && field === 'stockQuantity') {
        console.log('Không được phép cập nhật tồn kho trong chế độ chỉnh sửa');
        return;
      }

      const newVariants = variants.map((variant) => {
        if (variant.id === variantId) {
          // Tối ưu: Nếu field là SKU và value trống, tạo SKU tự động
          if (field === 'sku' && (!value || value.trim() === '')) {
            return { ...variant, [field]: generateVariantSKU(variant.name || 'VARIANT') };
          }
          // Cập nhật trường chính
          return { ...variant, [field]: value };
        }
        return variant;
      });

      setValue('variants', newVariants, { shouldDirty: true });
    },
    [variants, setValue, isEditMode, generateVariantSKU]
  );

  /**
   * Cập nhật hàng loạt các biến thể
   */
  const handleBulkEdit = useCallback(() => {
    if (!bulkEditField || bulkEditValue === '') return;

    // Nếu đang ở chế độ chỉnh sửa và field là stockQuantity, không cho phép cập nhật
    if (isEditMode && bulkEditField === 'stockQuantity') {
      console.log('Không được phép cập nhật tồn kho trong chế độ chỉnh sửa');
      setBulkEditField('');
      setBulkEditValue('');
      setAnchorEl(null);
      return;
    }

    // Đảm bảo giá trị hợp lệ cho các trường bắt buộc
    let processedValue = bulkEditValue;

    if (
      bulkEditField === 'price' ||
      bulkEditField === 'compareAtPrice' ||
      bulkEditField === 'costPrice' ||
      bulkEditField === 'stockQuantity'
    ) {
      // Đảm bảo giá trị là số và không âm
      processedValue = Math.max(0, Number(bulkEditValue) || 0);
    } else if (bulkEditField === 'sku' || bulkEditField === 'barcode') {
      // Giữ nguyên giá trị chuỗi
      processedValue = bulkEditValue;
    } else if (bulkEditField === 'isActive') {
      // Đảm bảo giá trị boolean
      processedValue = Boolean(bulkEditValue);
    } else {
      // Các trường số khác
      processedValue = Number(bulkEditValue);
    }

    const newVariants = variants.map((variant) => ({
      ...variant,
      [bulkEditField]: processedValue,
    }));

    setValue('variants', newVariants, { shouldDirty: true });
    setBulkEditField('');
    setBulkEditValue('');
    setAnchorEl(null);
  }, [bulkEditField, bulkEditValue, variants, setValue, isEditMode]);

  /**
   * Mở menu cập nhật hàng loạt
   * @param {Event} event - Event từ button
   */
  const handleBulkMenuOpen = useCallback((event) => {
    setAnchorEl(event.currentTarget);
  }, []);

  /**
   * Đóng menu cập nhật hàng loạt
   */
  const handleBulkMenuClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  /**
   * Mở dialog chọn hình ảnh cho biến thể
   * @param {string} variantId - ID của biến thể cần chọn hình
   */
  const handleOpenImageSelect = useCallback((variantId) => {
    setSelectedVariantForImage(variantId);
    setImageSelectOpen(true);
  }, []);

  /**
   * Lấy hình ảnh hiện tại của biến thể được chọn
   */
  const getSelectedVariantImage = useCallback(() => {
    if (!selectedVariantForImage) return null;
    const selectedVariant = variants.find((variant) => variant.id === selectedVariantForImage);
    return selectedVariant?.avatar || null;
  }, [selectedVariantForImage, variants]);

  /**
   * Tối ưu: Tìm URL tương ứng của File object trong danh sách images đã upload
   * @param {File} fileObject - File object cần tìm URL
   * @param {Array} imagesList - Danh sách images đã upload
   * @returns {string|null} - URL tương ứng hoặc null
   */
  const findUrlForFileObject = useCallback((fileObject, imagesList) => {
    if (!fileObject || !Array.isArray(imagesList)) return null;

    // Tìm URL matching dựa trên File properties
    for (const img of imagesList) {
      if (typeof img === 'string') {
        // Nếu đây là URL, skip vì chúng ta đang tìm URL cho File
        continue;
      }
      
      // Nếu đây là object có thể chứa thông tin File và URL
      if (img && typeof img === 'object') {
        // Case 1: Object có cả file và url
        if (img.file && img.url && img.file === fileObject) {
          return img.url;
        }
        
        // Case 2: Object là File object được wrap
        if (img.name === fileObject.name && img.size === fileObject.size) {
          // Tìm URL tương ứng - có thể cần thêm logic mapping
          // Tạm thời tạo blob URL nếu cần
          return URL.createObjectURL(fileObject);
        }
      }
    }

    // Fallback: Tạo blob URL tạm thời cho File object
    try {
      return URL.createObjectURL(fileObject);
    } catch (error) {
      console.error('Error creating blob URL:', error);
      return null;
    }
  }, []);

  /**
   * Tối ưu: Chọn hình ảnh cho biến thể - Không upload lại, chỉ lưu URL
   * @param {string|File} image - Hình ảnh được chọn (URL hoặc File object)
   */
  const handleSelectVariantImage = useCallback(
    async (image) => {
      try {
        if (!selectedVariantForImage) {
          console.error('Không có variant được chọn');
          return;
        }

        console.log('🎯 handleSelectVariantImage - Variant ID:', selectedVariantForImage);
        console.log('🖼️ handleSelectVariantImage - Image type:', typeof image);
        console.log('📋 handleSelectVariantImage - Available images:', images);

        let finalImageUrl = null;

        // Xử lý theo loại image
        if (typeof image === 'string') {
          // Case 1: Đã là URL string → sử dụng trực tiếp
          finalImageUrl = image;
          console.log('✅ Image is URL string:', finalImageUrl);
        } else if (image instanceof File || (image && typeof image === 'object' && image.name)) {
          // Case 2: Là File object → tìm URL tương ứng từ danh sách images
          console.log('🔍 Searching URL for File object...');
          
          // Tìm URL trong danh sách images đã upload
          const foundUrl = findUrlForFileObject(image, images);
          if (foundUrl) {
            finalImageUrl = foundUrl;
            console.log('✅ Found matching URL:', finalImageUrl);
          } else {
            console.warn('❌ Could not find URL for File object, creating blob URL');
            finalImageUrl = URL.createObjectURL(image);
          }
        } else {
          console.error('❌ Invalid image type:', image);
          return;
        }

        if (!finalImageUrl) {
          console.error('❌ Could not determine final image URL');
          return;
        }

        // Tìm variant trong danh sách
        const targetVariant = variants.find((v) => v.id === selectedVariantForImage);
        if (!targetVariant) {
          console.error('❌ Variant not found:', selectedVariantForImage);
          return;
        }

        // Phân biệt xử lý theo mode
        if (isEditMode && targetVariant.productId) {
          // Edit Mode: Cập nhật trực tiếp vào database với real ID
          console.log('🔧 Edit mode - Updating database directly');
          
          try {
            const updateResult = await updateData(
              'product_variants', 
              { avatar: finalImageUrl }, 
              { id: targetVariant.productId } // Sử dụng real product ID
            );

            if (updateResult.success) {
              console.log('✅ Avatar updated successfully in database');
              
              // Cập nhật form state để đồng bộ UI
              const newVariants = variants.map((variant) => {
                if (variant.id === selectedVariantForImage) {
                  return { ...variant, avatar: finalImageUrl };
                }
                return variant;
              });

              setValue('variants', newVariants, {
                shouldDirty: false, // Không mark dirty vì đã lưu vào DB
                shouldValidate: false,
                shouldTouch: false,
              });
              
              console.log('🎉 Edit mode avatar update completed successfully!');
            } else {
              console.error('❌ Failed to update avatar in database:', updateResult.error);
            }
          } catch (error) {
            console.error('❌ Error updating avatar in database:', error);
          }
        } else {
          // Create Mode: Chỉ cập nhật form state
          console.log('🆕 Create mode - Updating form state only');
          
          const newVariants = variants.map((variant) => {
            if (variant.id === selectedVariantForImage) {
              return { ...variant, avatar: finalImageUrl };
            }
            return variant;
          });

          setValue('variants', newVariants, {
            shouldDirty: true,
            shouldValidate: true,
            shouldTouch: true,
          });
          
          console.log('🎉 Create mode avatar update completed successfully!');
        }

        // Đóng dialog và reset state
        setImageSelectOpen(false);
        setSelectedVariantForImage(null);
        
      } catch (error) {
        console.error('❌ Error in handleSelectVariantImage:', error);
      }
    },
    [selectedVariantForImage, variants, images, setValue, isEditMode, findUrlForFileObject]
  );

  /**
   * Xóa hình ảnh của biến thể
   * @param {string} variantId - ID của biến thể cần xóa hình
   */
  const handleRemoveVariantImage = useCallback(
    async (variantId) => {
      try {
        const targetVariant = variants.find((v) => v.id === variantId);
        if (!targetVariant) {
          console.error('Variant not found:', variantId);
          return;
        }

        // Phân biệt xử lý theo mode
        if (isEditMode && targetVariant.productId) {
          // Edit Mode: Cập nhật trực tiếp vào database
          console.log('🔧 Edit mode - Removing avatar from database');
          
          const updateResult = await updateData(
            'product_variants', 
            { avatar: null }, 
            { id: targetVariant.productId }
          );

          if (updateResult.success) {
            console.log('✅ Avatar removed successfully from database');
            
            // Cập nhật form state
            const newVariants = variants.map((variant) => {
              if (variant.id === variantId) {
                return { ...variant, avatar: null };
              }
              return variant;
            });

            setValue('variants', newVariants, {
              shouldDirty: false,
              shouldValidate: false,
              shouldTouch: false,
            });
          } else {
            console.error('❌ Failed to remove avatar from database:', updateResult.error);
          }
        } else {
          // Create Mode: Chỉ cập nhật form state
          console.log('🆕 Create mode - Removing avatar from form state');
          
          const newVariants = variants.map((variant) => {
            if (variant.id === variantId) {
              return { ...variant, avatar: null };
            }
            return variant;
          });

          setValue('variants', newVariants, {
            shouldDirty: true,
            shouldValidate: true,
          });
        }
      } catch (error) {
        console.error('❌ Error removing variant image:', error);
      }
    },
    [variants, setValue, isEditMode]
  );

  return {
    variants,
    images,
    bulkEditField,
    setBulkEditField,
    bulkEditValue,
    setBulkEditValue,
    anchorEl,
    selectedVariantForImage,
    imageSelectOpen,
    setImageSelectOpen,
    generateVariants,
    handleVariantChange,
    handleBulkEdit,
    handleBulkMenuOpen,
    handleBulkMenuClose,
    handleOpenImageSelect,
    handleSelectVariantImage,
    handleRemoveVariantImage,
    getSelectedVariantImage,
  };
}
