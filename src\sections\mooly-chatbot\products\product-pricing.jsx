'use client';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';

import { Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

export default function ProductPricing({ watch }) {
  // const productType = watch('type');

  // Nếu là sản phẩm có biến thể và đã kích hoạt biến thể, không hiển thị phần giá và tồn kho chung
  // if (productType === PRODUCT_TYPES.VARIABLE) {
  //   return (
  //     <Card>
  //       <CardHeader title="<PERSON><PERSON><PERSON> & Tồn kho" />
  //       <CardContent>
  //         <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
  //           Gi<PERSON> và tồn kho được quản lý riêng cho từng biến thể sản phẩm.
  //         </Box>
  //       </CardContent>
  //     </Card>
  //   );
  // }

  return (
    <Card>
      <CardHeader title="Giá & Tồn kho" />

      <CardContent>
        <Stack spacing={2}>
          <Box
            sx={{
              rowGap: 2,
              columnGap: 2,
              display: 'grid',
              gridTemplateColumns: { xs: 'repeat(1, 1fr)', md: 'repeat(3, 1fr)' },
            }}
          >
            <Field.CurrencyInput
              name="price"
              label="Giá bán"
              placeholder="0"
              required
              size="small"
              currencySymbol="VND"
              slotProps={{
                inputLabel: { shrink: true },
              }}
            />

            <Field.CurrencyInput
              name="compareAtPrice"
              label="Giá so sánh"
              placeholder="0"
              size="small"
              currencySymbol="VND"
              slotProps={{
                inputLabel: { shrink: true },
              }}
            />

            <Field.Text
              name="stockQuantity"
              label="Tồn kho"
              placeholder="0"
              type="number"
              required
              size="small"
              slotProps={{ inputLabel: { shrink: true } }}
            />
          </Box>

          <Field.CurrencyInput
            name="costPrice"
            label="Giá nhập"
            placeholder="0"
            size="small"
            currencySymbol="VND"
            slotProps={{
              inputLabel: { shrink: true },
            }}
          />
        </Stack>
      </CardContent>
    </Card>
  );
}
