'use client';

/**
 * <PERSON><PERSON>c hằng số và cấu hình cho module sản phẩm
 */

// Tên bảng trong Supabase
export const TABLE_NAME = 'products';
export const PRODUCT_MEDIA_TABLE = 'product_media';
export const PRODUCT_VARIANTS_TABLE = 'product_variants';
export const PRODUCT_ATTRIBUTES_TABLE = 'product_attributes';
export const PRODUCT_ATTRIBUTE_VALUES_TABLE = 'product_attribute_values';
export const PRODUCT_VARIANT_ATTRIBUTES_TABLE = 'product_variant_attributes';

// Cấu hình đường dẫn lưu trữ
export const STORAGE_MODULE_NAME = 'products';
export const STORAGE_BUCKET = 'public';

// Cấu hình mặc định
export const DEFAULT_PRODUCT_OPTIONS = {
  orderBy: 'createdAt',
  ascending: false,
};

// Loại sản phẩm
export const PRODUCT_TYPES = {
  SIMPLE: 'simple',
  VARIABLE: 'variable',
  DIGITAL: 'digital',
  SERVICE: 'service',
  BUNDLE: 'bundle',
};

// Helper functions để kiểm tra loại sản phẩm
export const isVariableProduct = (product) => product && product.type === PRODUCT_TYPES.VARIABLE;

export const isSimpleProduct = (product) => product && product.type === PRODUCT_TYPES.SIMPLE;

export const isDigitalProduct = (product) => product && product.type === PRODUCT_TYPES.DIGITAL;

export const isServiceProduct = (product) => product && product.type === PRODUCT_TYPES.SERVICE;

export const isBundleProduct = (product) => product && product.type === PRODUCT_TYPES.BUNDLE;

// Trạng thái sản phẩm
export const PRODUCT_STATUS = {
  ACTIVE: 'active',
  DRAFT: 'draft',
  ARCHIVED: 'archived',
};

// Tên trường trong database - đồng bộ chính xác với schema DB
export const DB_FIELDS = {
  ID: 'id',
  NAME: 'name',
  DESCRIPTION: 'description',
  SHORT_DESCRIPTION: 'shortDescription',
  SKU: 'sku',
  BARCODE: 'barcode',
  CATEGORY_ID: 'categoryId',
  SELLING_PRICE: 'sellingPrice',
  STOCK_QUANTITY: 'stockQuantity',
  TYPE: 'type',
  IS_ACTIVE: 'isActive',
  META_TITLE: 'metaTitle',
  META_DESCRIPTION: 'metaDescription',
  META_KEYWORDS: 'metaKeywords',
  SLUG: 'slug',
  TRACK_INVENTORY: 'trackInventory',
  WEIGHT: 'weight',
  LENGTH: 'length',
  WIDTH: 'width',
  HEIGHT: 'height',
  IS_FEATURED: 'isFeatured',
  CREATED_AT: 'createdAt',
  UPDATED_AT: 'updatedAt',
  BOT_ID: 'botId',
};

// Tên trường trong UI form
export const FORM_FIELDS = {
  NAME: 'name',
  DESCRIPTION: 'description',
  SHORT_DESCRIPTION: 'shortDescription',
  BRAND: 'brand',
  CATEGORY: 'categoryId',
  PRODUCT_TYPE: 'type',
  SKU: 'sku',
  BARCODE: 'barcode',
  SELLING_PRICE: 'sellingPrice',
  STOCK: 'stock',
  WEIGHT: 'weight',
  LENGTH: 'length',
  WIDTH: 'width',
  HEIGHT: 'height',
  IS_FEATURED: 'isFeatured',
  IMAGES: 'images',
  VIDEOS: 'videos',
  ATTRIBUTES: 'attributes',
  VARIANTS: 'variants',
  META_TITLE: 'metaTitle',
  META_DESCRIPTION: 'metaDescription',
  META_KEYWORDS: 'metaKeywords',
  STATUS: 'status',
  // Thêm các trường DB để đồng bộ
  SELLING_PRICE_DB: 'sellingPrice',
  STOCK_QUANTITY: 'stockQuantity',
  IS_ACTIVE: 'isActive',
  TRACK_INVENTORY: 'trackInventory',
  SEO_TITLE: 'seoTitle',
  SEO_DESCRIPTION: 'seoDescription',
  META_KEYWORDS_DB: 'metaKeywords',
  BOT_ID: 'botId',
};

// Mapping giữa trường form và trường database
export const FORM_TO_DB_MAPPING = {
  [FORM_FIELDS.NAME]: DB_FIELDS.NAME,
  [FORM_FIELDS.DESCRIPTION]: DB_FIELDS.DESCRIPTION,
  [FORM_FIELDS.SHORT_DESCRIPTION]: DB_FIELDS.SHORT_DESCRIPTION,
  [FORM_FIELDS.CATEGORY]: DB_FIELDS.CATEGORY_ID,
  [FORM_FIELDS.PRODUCT_TYPE]: DB_FIELDS.TYPE,
  [FORM_FIELDS.SKU]: DB_FIELDS.SKU,
  [FORM_FIELDS.BARCODE]: DB_FIELDS.BARCODE,
  [FORM_FIELDS.PRICE]: DB_FIELDS.PRICE,
  [FORM_FIELDS.SALE_PRICE]: DB_FIELDS.SALE_PRICE,
  [FORM_FIELDS.STOCK]: DB_FIELDS.STOCK_QUANTITY,
  [FORM_FIELDS.WEIGHT]: DB_FIELDS.WEIGHT,
  [FORM_FIELDS.LENGTH]: DB_FIELDS.LENGTH,
  [FORM_FIELDS.WIDTH]: DB_FIELDS.WIDTH,
  [FORM_FIELDS.HEIGHT]: DB_FIELDS.HEIGHT,
  [FORM_FIELDS.IS_FEATURED]: DB_FIELDS.IS_FEATURED,
  [FORM_FIELDS.META_TITLE]: DB_FIELDS.META_TITLE,
  [FORM_FIELDS.META_DESCRIPTION]: DB_FIELDS.META_DESCRIPTION,
  [FORM_FIELDS.META_KEYWORDS]: DB_FIELDS.META_KEYWORDS,
  [FORM_FIELDS.STATUS]: DB_FIELDS.IS_ACTIVE,
  // Thêm mapping cho các trường DB
  [FORM_FIELDS.SALE_PRICE_DB]: DB_FIELDS.SALE_PRICE,
  [FORM_FIELDS.STOCK_QUANTITY]: DB_FIELDS.STOCK_QUANTITY,
  [FORM_FIELDS.IS_ACTIVE]: DB_FIELDS.IS_ACTIVE,
  [FORM_FIELDS.TRACK_INVENTORY]: DB_FIELDS.TRACK_INVENTORY,
  [FORM_FIELDS.SEO_TITLE]: DB_FIELDS.META_TITLE,
  [FORM_FIELDS.SEO_DESCRIPTION]: DB_FIELDS.META_DESCRIPTION,
  [FORM_FIELDS.BOT_ID]: DB_FIELDS.BOT_ID,
};
