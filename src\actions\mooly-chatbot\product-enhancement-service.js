'use client';

/**
 * Product Enhancement Service
 * Mở rộng các tính năng quản lý sản phẩm với:
 * - Bundle management (quản lý sản phẩm combo)
 * - Business type filtering (lọc theo loại hình kinh doanh)
 * - Bulk operations (thao tác hàng loạt)
 * - Advanced product operations
 * - Analytics & reporting
 * - Import/Export capabilities
 */

import { BUSINESS_TYPES } from './business-config-service';
import { TABLE_NAME, PRODUCT_TYPES } from './product-constants';
import { callRPC, fetchData, createData, updateData, deleteData } from './supabase-utils';

// Bundle related tables
const PRODUCT_BUNDLES_TABLE = 'product_bundles';
const PRODUCT_BUNDLE_ITEMS_TABLE = 'product_bundle_items';

// Utility functions
/**
 * Kiểm tra xem business type có hỗ trợ feature không
 * @param {string} businessType - <PERSON>ại hình kinh doanh
 * @param {string} feature - T<PERSON><PERSON> năng cần kiểm tra
 * @returns {boolean} - C<PERSON> hỗ trợ hay không
 */
function isFeatureSupported(businessType, feature) {
  const businessConfig = BUSINESS_TYPES[businessType];
  return businessConfig && businessConfig.features && businessConfig.features[feature];
}

/**
 * Lấy danh sách product types được phép cho business type
 * @param {string} businessType - Loại hình kinh doanh
 * @returns {Array} - Danh sách product types
 */
function getAllowedProductTypes(businessType) {
  const businessConfig = BUSINESS_TYPES[businessType];
  if (!businessConfig) return [];

  const allowedTypes = [];

  if (businessConfig.features.physicalProducts) {
    allowedTypes.push(PRODUCT_TYPES.SIMPLE, PRODUCT_TYPES.VARIABLE);
  }
  if (businessConfig.features.digitalProducts) {
    allowedTypes.push(PRODUCT_TYPES.DIGITAL);
  }
  if (businessConfig.features.services) {
    allowedTypes.push(PRODUCT_TYPES.SERVICE);
  }
  if (businessConfig.features.bundles) {
    allowedTypes.push(PRODUCT_TYPES.BUNDLE);
  }

  return allowedTypes;
}

/**
 * 🎁 BUNDLE MANAGEMENT FUNCTIONS
 */

/**
 * Tạo sản phẩm bundle mới
 * @param {Object} bundleData - Dữ liệu bundle
 * @param {Array} bundleItems - Danh sách sản phẩm trong bundle
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createProductBundle(bundleData, bundleItems = []) {
  try {
    // 1. Tạo bundle chính
    const bundleResult = await createData(PRODUCT_BUNDLES_TABLE, {
      name: bundleData.name,
      description: bundleData.description,
      bundleType: bundleData.bundleType || 'fixed', // fixed, dynamic
      discountType: bundleData.discountType || 'percentage', // percentage, fixed_amount
      discountValue: bundleData.discountValue || 0,
      minQuantity: bundleData.minQuantity || 1,
      maxQuantity: bundleData.maxQuantity || null,
      isActive: bundleData.isActive !== false,
      validFrom: bundleData.validFrom || null,
      validTo: bundleData.validTo || null,
      metadata: bundleData.metadata || {}
    });

    if (!bundleResult.success) {
      throw new Error(bundleResult.error?.message || 'Không thể tạo bundle');
    }

    const bundleId = bundleResult.data.id;

    // 2. Thêm các sản phẩm vào bundle
    if (bundleItems.length > 0) {
      const bundleItemsData = bundleItems.map(item => ({
        bundleId,
        productId: item.productId,
        variantId: item.variantId || null,
        quantity: item.quantity || 1,
        discountType: item.discountType || 'none',
        discountValue: item.discountValue || 0,
        isRequired: item.isRequired !== false,
        sortOrder: item.sortOrder || 0
      }));

      const itemsResult = await createData(PRODUCT_BUNDLE_ITEMS_TABLE, bundleItemsData);

      if (!itemsResult.success) {
        // Rollback: xóa bundle đã tạo
        await deleteData(PRODUCT_BUNDLES_TABLE, { id: bundleId });
        throw new Error('Không thể thêm sản phẩm vào bundle');
      }
    }

    return {
      success: true,
      data: {
        bundle: bundleResult.data,
        items: bundleItems
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo bundle'
    };
  }
}

/**
 * Lấy danh sách bundles
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductBundles(options = {}) {
  return fetchData(PRODUCT_BUNDLES_TABLE, {
    ...options,
    columns: `
      *,
      items:product_bundle_items(
        *,
        product:products(id, name, price, images),
        variant:product_variants(id, name, price, avatar)
      )
    `
  });
}

/**
 * Cập nhật bundle
 * @param {string} bundleId - ID bundle
 * @param {Object} bundleData - Dữ liệu cập nhật
 * @param {Array} bundleItems - Danh sách sản phẩm mới (optional)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateProductBundle(bundleId, bundleData, bundleItems = null) {
  try {
    // 1. Cập nhật thông tin bundle
    const bundleResult = await updateData(PRODUCT_BUNDLES_TABLE, bundleData, { id: bundleId });

    if (!bundleResult.success) {
      throw new Error('Không thể cập nhật bundle');
    }

    // 2. Cập nhật items nếu có
    if (bundleItems !== null) {
      // Xóa items cũ
      await deleteData(PRODUCT_BUNDLE_ITEMS_TABLE, { bundleId });

      // Thêm items mới
      if (bundleItems.length > 0) {
        const bundleItemsData = bundleItems.map(item => ({
          bundleId,
          productId: item.productId,
          variantId: item.variantId || null,
          quantity: item.quantity || 1,
          discountType: item.discountType || 'none',
          discountValue: item.discountValue || 0,
          isRequired: item.isRequired !== false,
          sortOrder: item.sortOrder || 0
        }));

        await createData(PRODUCT_BUNDLE_ITEMS_TABLE, bundleItemsData);
      }
    }

    return {
      success: true,
      data: bundleResult.data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật bundle'
    };
  }
}

/**
 * Xóa bundle
 * @param {string} bundleId - ID bundle
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteProductBundle(bundleId) {
  try {
    // 1. Xóa items trước
    await deleteData(PRODUCT_BUNDLE_ITEMS_TABLE, { bundleId });

    // 2. Xóa bundle
    const result = await deleteData(PRODUCT_BUNDLES_TABLE, { id: bundleId });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa bundle'
    };
  }
}

/**
 * 🏢 BUSINESS TYPE FILTERING FUNCTIONS
 */

/**
 * Lấy sản phẩm theo loại hình kinh doanh
 * @param {string} businessType - Loại hình kinh doanh (retail, digital, services, hybrid)
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getProductsByBusinessType(businessType, options = {}) {
  const businessConfig = BUSINESS_TYPES[businessType];

  if (!businessConfig) {
    return {
      success: false,
      error: 'Loại hình kinh doanh không hợp lệ'
    };
  }

  // Xác định các loại sản phẩm phù hợp
  const allowedProductTypes = getAllowedProductTypes(businessType);

  // Thêm filter theo product type
  const filters = {
    ...options.filters,
    type: { in: allowedProductTypes }
  };

  return fetchData(TABLE_NAME, {
    ...options,
    filters
  });
}

/**
 * Lọc các trường sản phẩm theo business type
 * @param {Object} productData - Dữ liệu sản phẩm
 * @param {string} businessType - Loại hình kinh doanh
 * @returns {Object} - Dữ liệu sản phẩm đã lọc
 */
export function filterProductFieldsByBusinessType(productData, businessType) {
  const businessConfig = BUSINESS_TYPES[businessType];

  if (!businessConfig) {
    return productData;
  }

  const filteredData = { ...productData };

  // Ẩn các trường không phù hợp
  if (!businessConfig.features.inventory) {
    delete filteredData.stockQuantity;
    delete filteredData.trackInventory;
    delete filteredData.lowStockThreshold;
  }

  if (!businessConfig.features.shipping) {
    delete filteredData.weight;
    delete filteredData.length;
    delete filteredData.width;
    delete filteredData.height;
    delete filteredData.shippingClass;
  }

  if (!businessConfig.features.variants) {
    delete filteredData.hasVariants;
    delete filteredData.variants;
    delete filteredData.attributes;
  }

  if (!businessConfig.features.digitalDelivery) {
    delete filteredData.digitalProductInfo;
    delete filteredData.downloadLinks;
    delete filteredData.licenseKeys;
  }

  if (!businessConfig.features.appointmentBooking) {
    delete filteredData.serviceInfo;
    delete filteredData.duration;
    delete filteredData.bookingSettings;
  }

  return filteredData;
}

/**
 * 📦 BULK OPERATIONS FUNCTIONS
 */

/**
 * Cập nhật hàng loạt sản phẩm
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @param {Object} productUpdateData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function bulkUpdateProducts(productIds, productUpdateData) {
  try {
    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw new Error('Danh sách sản phẩm không hợp lệ');
    }

    // Sử dụng RPC function để cập nhật hàng loạt
    const result = await callRPC('bulk_update_products', {
      product_ids: productIds,
      update_data: productUpdateData
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật hàng loạt'
    };
  }
}

/**
 * Xóa hàng loạt sản phẩm
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function bulkDeleteProducts(productIds) {
  try {
    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw new Error('Danh sách sản phẩm không hợp lệ');
    }

    const result = await deleteData(TABLE_NAME, {
      id: { in: productIds }
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa hàng loạt'
    };
  }
}

/**
 * Cập nhật trạng thái hàng loạt
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @param {boolean} isActive - Trạng thái mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function bulkUpdateProductStatus(productIds, isActive) {
  return bulkUpdateProducts(productIds, { isActive });
}

/**
 * Cập nhật giá hàng loạt
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @param {Object} priceData - Dữ liệu giá { sellingPrice }
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function bulkUpdateProductPrices(productIds, priceData) {
  return bulkUpdateProducts(productIds, priceData);
}

/**
 * Cập nhật danh mục hàng loạt
 * @param {Array} productIds - Danh sách ID sản phẩm
 * @param {string} categoryId - ID danh mục mới
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function bulkUpdateProductCategory(productIds, categoryId) {
  return bulkUpdateProducts(productIds, { categoryId });
}

/**
 * 🔍 ADVANCED SEARCH & FILTERING FUNCTIONS
 */

/**
 * Tìm kiếm sản phẩm nâng cao
 * @param {Object} searchParams - Tham số tìm kiếm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function advancedProductSearch(searchParams) {
  try {
    const {
      query,           // Từ khóa tìm kiếm
      categoryIds,     // Danh sách ID danh mục
      productTypes,    // Loại sản phẩm
      priceRange,      // Khoảng giá { min, max }
      stockStatus,     // Trạng thái tồn kho (in_stock, out_of_stock, low_stock)
      isActive,        // Trạng thái hoạt động
      businessType,    // Loại hình kinh doanh
      sortBy,          // Sắp xếp theo (name, price, created_at, stock_quantity)
      sortOrder,       // Thứ tự (asc, desc)
      page = 1,        // Trang
      limit = 20       // Số lượng mỗi trang
    } = searchParams;

    let filters = {};
    let textSearch = null;

    // Text search
    if (query) {
      textSearch = query;
    }

    // Category filter
    if (categoryIds && categoryIds.length > 0) {
      filters.categoryId = { in: categoryIds };
    }

    // Product type filter
    if (productTypes && productTypes.length > 0) {
      filters.type = { in: productTypes };
    }

    // Price range filter
    if (priceRange) {
      if (priceRange.min !== undefined) {
        filters.price = { ...filters.price, gte: priceRange.min };
      }
      if (priceRange.max !== undefined) {
        filters.price = { ...filters.price, lte: priceRange.max };
      }
    }

    // Stock status filter
    if (stockStatus) {
      switch (stockStatus) {
        case 'in_stock':
          filters.stockQuantity = { gt: 0 };
          break;
        case 'out_of_stock':
          filters.stockQuantity = { eq: 0 };
          break;
        case 'low_stock':
          // Sẽ cần thêm logic để xác định low stock threshold
          filters.stockQuantity = { lt: 10 };
          break;
        default:
          // Không áp dụng filter nếu stockStatus không hợp lệ
          break;
      }
    }

    // Active status filter
    if (isActive !== undefined) {
      filters.isActive = isActive;
    }

    // Business type filter
    if (businessType) {
      const allowedProductTypes = getAllowedProductTypes(businessType);
      if (allowedProductTypes.length > 0) {
        filters.type = { in: allowedProductTypes };
      }
    }

    // Build query options
    const options = {
      filters,
      textSearch,
      orderBy: sortBy || 'createdAt',
      ascending: sortOrder === 'asc',
      page,
      limit,
      columns: `
        *,
        category:product_categories(id, name),
        variants:product_variants(id, name, selling_price, stockQuantity)
      `
    };

    return fetchData(TABLE_NAME, options);
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tìm kiếm sản phẩm'
    };
  }
}

/**
 * Lấy sản phẩm liên quan
 * @param {string} productId - ID sản phẩm gốc
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getRelatedProducts(productId, options = {}) {
  try {
    // Lấy thông tin sản phẩm gốc
    const productResult = await fetchData(TABLE_NAME, {
      filters: { id: productId },
      single: true,
      columns: 'id, categoryId, type, price'
    });

    if (!productResult.success || !productResult.data) {
      throw new Error('Không tìm thấy sản phẩm');
    }

    const product = productResult.data;
    const { limit = 8 } = options;

    // Tìm sản phẩm liên quan theo danh mục và loại
    const filters = {
      id: { neq: productId }, // Loại trừ sản phẩm hiện tại
      isActive: true
    };

    // Ưu tiên sản phẩm cùng danh mục
    if (product.categoryId) {
      filters.categoryId = product.categoryId;
    }

    // Ưu tiên sản phẩm cùng loại
    if (product.type) {
      filters.type = product.type;
    }

    return fetchData(TABLE_NAME, {
      filters,
      limit,
      orderBy: 'createdAt',
      ascending: false,
      columns: 'id, name, selling_price, images, type'
    });
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy sản phẩm liên quan'
    };
  }
}

/**
 * 📊 ANALYTICS & REPORTING FUNCTIONS
 */

/**
 * Lấy thống kê sản phẩm
 * @param {Object} options - Tùy chọn thống kê
 * @returns {Promise<Object>} - Kết quả thống kê
 */
export async function getProductAnalytics(options = {}) {
  try {
    const { businessType, dateRange } = options;

    // Sử dụng RPC function để lấy thống kê
    const result = await callRPC('get_product_analytics', {
      business_type: businessType,
      date_from: dateRange?.from,
      date_to: dateRange?.to
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy thống kê sản phẩm'
    };
  }
}

/**
 * Lấy sản phẩm bán chạy
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getBestSellingProducts(options = {}) {
  try {
    const { limit = 10, businessType, dateRange } = options;

    const result = await callRPC('get_best_selling_products', {
      limit,
      business_type: businessType,
      date_from: dateRange?.from,
      date_to: dateRange?.to
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy sản phẩm bán chạy'
    };
  }
}

/**
 * Lấy sản phẩm sắp hết hàng
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getLowStockProducts(options = {}) {
  try {
    const { threshold = 10, businessType } = options;

    let filters = {
      isActive: true,
      stockQuantity: { lte: threshold }
    };

    // Filter by business type if specified
    if (businessType) {
      if (isFeatureSupported(businessType, 'inventory')) {
        const allowedProductTypes = getAllowedProductTypes(businessType).filter(type =>
          type === PRODUCT_TYPES.SIMPLE || type === PRODUCT_TYPES.VARIABLE
        );

        if (allowedProductTypes.length > 0) {
          filters.type = { in: allowedProductTypes };
        }
      } else {
        // Business type không hỗ trợ inventory
        return {
          success: true,
          data: []
        };
      }
    }

    return fetchData(TABLE_NAME, {
      filters,
      orderBy: 'stockQuantity',
      ascending: true,
      columns: 'id, name, sku, stockQuantity, selling_price, images'
    });
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi lấy sản phẩm sắp hết hàng'
    };
  }
}

/**
 * 🔄 IMPORT/EXPORT FUNCTIONS
 */

/**
 * Import sản phẩm từ CSV
 * @param {Array} csvData - Dữ liệu CSV
 * @param {Object} options - Tùy chọn import
 * @returns {Promise<Object>} - Kết quả import
 */
export async function importProductsFromCSV(csvData, options = {}) {
  try {
    const { businessType, validateOnly = false } = options;

    // Validate data format
    const validationErrors = [];
    const validProducts = [];

    csvData.forEach((row, index) => {
      const errors = validateProductData(row, businessType);
      if (errors.length > 0) {
        validationErrors.push({
          row: index + 1,
          errors
        });
      } else {
        validProducts.push(row);
      }
    });

    if (validateOnly) {
      return {
        success: true,
        data: {
          validCount: validProducts.length,
          errorCount: validationErrors.length,
          errors: validationErrors
        }
      };
    }

    // Import valid products
    if (validProducts.length > 0) {
      const result = await createData(TABLE_NAME, validProducts);

      return {
        success: true,
        data: {
          imported: result.data?.length || 0,
          errors: validationErrors
        }
      };
    }

    return {
      success: false,
      error: 'Không có sản phẩm hợp lệ để import'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi import sản phẩm'
    };
  }
}

/**
 * Validate product data
 * @param {Object} productData - Dữ liệu sản phẩm
 * @param {string} businessType - Loại hình kinh doanh
 * @returns {Array} - Danh sách lỗi
 */
function validateProductData(productData, businessType) {
  const errors = [];

  // Required fields
  if (!productData.name) {
    errors.push('Tên sản phẩm là bắt buộc');
  }

  if (!productData.price || productData.price <= 0) {
    errors.push('Giá sản phẩm phải lớn hơn 0');
  }

  // Business type specific validation
  if (businessType) {
    // Validate product type
    if (productData.type) {
      const allowedTypes = getAllowedProductTypes(businessType);
      if (!allowedTypes.includes(productData.type)) {
        errors.push(`Loại sản phẩm ${productData.type} không phù hợp với loại hình kinh doanh`);
      }
    }

    // Validate inventory fields
    if (!isFeatureSupported(businessType, 'inventory') && productData.stockQuantity !== undefined) {
      errors.push('Loại hình kinh doanh này không hỗ trợ quản lý tồn kho');
    }

    // Validate shipping fields
    if (!isFeatureSupported(businessType, 'shipping') && (productData.weight || productData.length)) {
      errors.push('Loại hình kinh doanh này không hỗ trợ thông tin vận chuyển');
    }
  }

  return errors;
}
