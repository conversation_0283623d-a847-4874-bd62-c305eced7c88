'use client';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

/**
 * Validate dữ liệu sản phẩm trướ<PERSON> khi submit
 * @param {Object} data - D<PERSON> liệu sản phẩm
 * @param {string} productType - <PERSON>ại sản phẩm
 * @param {boolean} isEditMode - <PERSON><PERSON> phải chế độ edit không
 * @returns {Object} - { isValid: boolean, errors: string[] }
 */
export function validateProductData(data, productType = PRODUCT_TYPES.SIMPLE, isEditMode = false) {
  const errors = [];

  // Validation chung cho tất cả sản phẩm
  if (!data.name || data.name.trim() === '') {
    errors.push('Tên sản phẩm là bắt buộc');
  }

  if (!data.categoryId || data.categoryId.trim() === '') {
    errors.push('<PERSON><PERSON> mục sản phẩm là bắt buộc');
  }

  // SKU sẽ được tự động tạo nếu để trống, không cần validate ở đây
  // if (!data.sku || data.sku.trim() === '') {
  //   errors.push('Mã SKU là bắt buộc');
  // }

  if (!data.price || data.price <= 0) {
    errors.push('Giá sản phẩm phải lớn hơn 0');
  }

  if (!data.images || data.images.length === 0) {
    errors.push('Ít nhất một hình ảnh sản phẩm là bắt buộc');
  }

  // Validation cho số lượng tồn kho
  if (data.trackInventory && (data.stockQuantity === null || data.stockQuantity === undefined || data.stockQuantity < 0)) {
    errors.push('Số lượng tồn kho phải lớn hơn hoặc bằng 0');
  }

  // Validation riêng cho từng loại sản phẩm
  switch (productType) {
    case PRODUCT_TYPES.SIMPLE:
      // Sản phẩm simple không cần validation thêm
      break;

    case PRODUCT_TYPES.VARIABLE:
      // Validation cho sản phẩm có biến thể - chỉ validate trong create mode
      if (!isEditMode) {
        if (!data.attributes || data.attributes.length === 0) {
          errors.push('Sản phẩm có biến thể cần ít nhất một thuộc tính');
        }
      }
      break;

    case PRODUCT_TYPES.DIGITAL:
      // Validation cho sản phẩm số
      if (!data.digitalProductInfo || !data.digitalProductInfo.downloadUrl) {
        errors.push('Sản phẩm số cần có link tải xuống');
      }
      break;

    case PRODUCT_TYPES.SERVICE:
      // Validation cho dịch vụ
      if (!data.serviceInfo || !data.serviceInfo.duration) {
        errors.push('Dịch vụ cần có thời gian thực hiện');
      }
      break;

    default:
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Làm sạch dữ liệu sản phẩm trước khi gửi lên server
 * @param {Object} data - Dữ liệu sản phẩm
 * @param {string} productType - Loại sản phẩm
 * @returns {Object} - Dữ liệu đã được làm sạch
 */
export function sanitizeProductData(data, productType = PRODUCT_TYPES.SIMPLE) {
  const cleanData = { ...data };

  // Làm sạch các trường text
  if (cleanData.name) {
    cleanData.name = cleanData.name.trim();
  }

  if (cleanData.description) {
    cleanData.description = cleanData.description.trim();
  }

  if (cleanData.shortDescription) {
    cleanData.shortDescription = cleanData.shortDescription.trim();
  }

  if (cleanData.sku) {
    cleanData.sku = cleanData.sku.trim().toUpperCase();
  }

  // Đảm bảo giá trị số hợp lệ
  cleanData.sellingPrice = Number(cleanData.sellingPrice) || 0;
  cleanData.stockQuantity = Number(cleanData.stockQuantity) || 0;

  if (cleanData.costPrice) {
    cleanData.costPrice = Number(cleanData.costPrice) || null;
  }

  // Tối ưu dữ liệu theo loại sản phẩm
  switch (productType) {
    case PRODUCT_TYPES.SIMPLE:
      // Loại bỏ các trường không cần thiết cho sản phẩm simple
      cleanData.attributes = [];
      cleanData.variants = [];
      cleanData.hasVariants = false;
      cleanData.tags = cleanData.tags || [];
      cleanData.gender = [];
      cleanData.saleLabel = { enabled: false, content: '' };
      cleanData.newLabel = { enabled: false, content: '' };
      cleanData.metaKeywords = [];
      cleanData.isFeatured = false;
      cleanData.taxes = null;
      cleanData.includeTaxes = false;
      break;

    case PRODUCT_TYPES.VARIABLE:
      // Đảm bảo có attributes và variants
      cleanData.attributes = cleanData.attributes || [];
      cleanData.variants = cleanData.variants || [];
      cleanData.hasVariants = true;
      break;

    case PRODUCT_TYPES.DIGITAL:
      // Đảm bảo có thông tin sản phẩm số
      cleanData.digitalProductInfo = cleanData.digitalProductInfo || {};
      cleanData.trackInventory = false; // Sản phẩm số không cần theo dõi tồn kho
      break;

    case PRODUCT_TYPES.SERVICE:
      // Đảm bảo có thông tin dịch vụ
      cleanData.serviceInfo = cleanData.serviceInfo || {};
      cleanData.trackInventory = false; // Dịch vụ không cần theo dõi tồn kho
      break;

    default:
      break;
  }

  // Đảm bảo các trường bắt buộc có giá trị mặc định
  cleanData.isActive = cleanData.isActive !== false;
  cleanData.trackInventory = cleanData.trackInventory !== false;

  // Đảm bảo arrays không null
  cleanData.images = cleanData.images || [];
  cleanData.tags = cleanData.tags || [];
  cleanData.metaKeywords = cleanData.metaKeywords || [];

  // Đảm bảo objects không null
  cleanData.marketingInfo = cleanData.marketingInfo || {};
  cleanData.inventorySettings = cleanData.inventorySettings || {};
  cleanData.pricingSettings = cleanData.pricingSettings || {};
  cleanData.dimensions = cleanData.dimensions || {};

  return cleanData;
}

/**
 * Tạo slug từ tên sản phẩm
 * @param {string} name - Tên sản phẩm
 * @returns {string} - Slug
 */
export function generateSlug(name) {
  if (!name) return '';
  
  return name
    .toLowerCase()
    .trim()
    .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
    .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
    .replace(/[ìíịỉĩ]/g, 'i')
    .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
    .replace(/[ùúụủũưừứựửữ]/g, 'u')
    .replace(/[ỳýỵỷỹ]/g, 'y')
    .replace(/đ/g, 'd')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

/**
 * Kiểm tra SKU có hợp lệ không
 * @param {string} sku - Mã SKU
 * @returns {boolean} - True nếu hợp lệ
 */
export function isValidSKU(sku) {
  if (!sku || typeof sku !== 'string') return false;
  
  // SKU phải có ít nhất 3 ký tự và chỉ chứa chữ, số, dấu gạch ngang và gạch dưới
  const skuRegex = /^[A-Z0-9_-]{3,}$/;
  return skuRegex.test(sku.toUpperCase());
}
