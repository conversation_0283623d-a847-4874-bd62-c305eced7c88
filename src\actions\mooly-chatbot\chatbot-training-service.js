'use client';

import storageService from './storage-service';
import { fetchData, createData, deleteData } from './supabase-utils';
import { updateChatbot, createChatbot, getChatbotById } from './chatbot-service';

// Tên bảng trong Supabase
const FAQ_TABLE = 'chatbot_faqs';
const INSTRUCTION_TABLE = 'chatbot_instructions';
const CHATBOT_FEATURES_TABLE = 'chatbot_features';

/**
 * Lưu dữ liệu training cho chatbot
 * @param {Object} trainingData - Dữ liệu training
 * @returns {Promise<Object>} - K<PERSON>t quả từ API
 */
/**
 * Xử lý tải lên avatar chatbot
 * @param {File} avatarFile - File avatar cần tải lên
 * @param {string|null} oldAvatarUrl - URL avatar cũ (nếu có)
 * @returns {Promise<Object>} - Kết qu<PERSON> từ API với URL avatar
 */
export async function uploadChatbotAvatar(avatarFile, oldAvatarUrl = null) {
  try {
    // Nếu không có file mới, trả về URL cũ hoặc null
    if (!avatarFile) return { success: true, avatarUrl: oldAvatarUrl };

    // Xóa avatar cũ nếu có
    if (oldAvatarUrl) {
      await deleteChatbotAvatar(oldAvatarUrl);
    }

    // Lấy tenant_id từ middleware (sẽ được xử lý tự động)
    const { getCachedTenantId } = await import('./tenant-middleware');
    const tenantId = await getCachedTenantId();

    if (!tenantId) {
      return { success: false, error: 'Could not determine tenant ID', avatarUrl: null };
    }

    // Tạo tên file duy nhất
    const fileName = storageService.generateUniqueFileName(avatarFile.name);
    // Tạo đường dẫn lưu trữ
    const filePath = storageService.buildFilePath('chatbots', tenantId, fileName);
    // Tải lên avatar mới
    const uploadResult = await storageService.uploadFile('public', filePath, avatarFile, {
      upsert: true,
      cacheControl: '3600',
    });

    if (uploadResult.success) {
      return { success: true, avatarUrl: uploadResult.publicUrl };
    }

    return {
      success: false,
      error: uploadResult.error,
      avatarUrl: null,
    };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return { success: false, error, avatarUrl: null };
  }
}

/**
 * Xóa avatar chatbot
 * @param {string} avatarUrl - URL avatar cần xóa
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChatbotAvatar(avatarUrl) {
  try {
    if (!avatarUrl) return { success: true };

    // Trích xuất đường dẫn từ URL
    const avatarPath = storageService.extractPathFromUrl(avatarUrl);
    if (!avatarPath) {
      console.warn('Could not extract path from avatar URL:', avatarUrl);
      return { success: false, error: 'Invalid avatar URL format' };
    }

    // Kiểm tra xem đường dẫn có chứa 'public/' ở đầu không
    const cleanPath = avatarPath.startsWith('public/')
      ? avatarPath.substring(7) // Cắt bỏ 'public/' ở đầu nếu có
      : avatarPath;

    // Kiểm tra xem file có tồn tại trước khi xóa
    const fileExists = await storageService.checkFileExists('public', cleanPath);
    if (!fileExists.exists) {
      console.warn('Image file not found in storage:', cleanPath);
      // Vẫn trả về success vì mục tiêu là đảm bảo hình ảnh không còn tồn tại
      return { success: true, data: null, message: 'File not found in storage' };
    }

    // Xóa hình ảnh từ storage
    const deleteResult = await storageService.deleteFiles('public', cleanPath);

    return deleteResult;
  } catch (error) {
    console.error('Error deleting avatar:', error);
    return { success: false, error };
  }
}

export async function saveChatbotTrainingData(trainingData) {
  try {
    // Kiểm tra xem chatbot đã tồn tại chưa
    let chatbotId = trainingData.id;
    let result;

    // Xử lý avatar nếu có
    let avatarUrl = trainingData.avatarUrl;

    // Nếu avatarUrl là File object (người dùng đã chọn file mới)
    if (trainingData.avatarUrl && typeof trainingData.avatarUrl === 'object') {
      // Xử lý upload avatar và xóa avatar cũ nếu cần
      const avatarFile = trainingData.avatarUrl;
      const uploadResult = await uploadChatbotAvatar(
        avatarFile,
        chatbotId ? trainingData.oldAvatarUrl : null
      );

      if (uploadResult.success) {
        // Lưu URL của file đã upload
        avatarUrl = uploadResult.avatarUrl;
      } else {
        console.error('Failed to upload avatar:', uploadResult.error);
        // Vẫn tiếp tục lưu chatbot nhưng với URL avatar cũ hoặc null
        avatarUrl = chatbotId ? trainingData.oldAvatarUrl : null;
      }
    }

    // Tạo instruction từ instructionItems nếu có
    let instruction = trainingData.instruction || '';
    if (trainingData.instructionItems && trainingData.instructionItems.length > 0) {
      // Tạo instruction từ các mục hướng dẫn
      instruction = trainingData.instructionItems
        .map((item) => `${item.title}:\n${item.content}`)
        .join('\n\n');
    }

    // Chuẩn bị dữ liệu chatbot
    const chatbotData = {
      name: trainingData.name,
      avatarUrl,
      instruction,
      status: trainingData.status || 'draft',
      training_data: {
        infoItems: trainingData.infoItems || [],
        instructionItems: trainingData.instructionItems || [],
        features: trainingData.features || [],
      },
      // Thêm các trường mặc định nếu là chatbot mới
      ...(chatbotId
        ? {}
        : {
            greetingMessage: 'Xin chào! Tôi có thể giúp gì cho bạn?',
            fallbackMessage:
              'Xin lỗi, tôi không hiểu câu hỏi của bạn. Vui lòng thử lại với câu hỏi khác.',
            primaryColor: '#3B82F6',
            position: 'bottom-right',
            isActive: true,
          }),
    };

    if (chatbotId) {
      // Cập nhật chatbot hiện có
      result = await updateChatbot(chatbotId, chatbotData);
    } else {
      // Tạo chatbot mới
      result = await createChatbot(chatbotData);
      if (result.success) {
        chatbotId = result.data.id;
      }
    }

    if (!result.success) {
      return result;
    }

    // Lưu dữ liệu FAQs từ infoItems
    if (trainingData.infoItems && trainingData.infoItems.length > 0) {
      await saveChatbotFAQs(chatbotId, trainingData.infoItems);
    }

    // Lưu instruction items
    if (trainingData.instructionItems && trainingData.instructionItems.length > 0) {
      await saveChatbotInstructionItems(chatbotId, trainingData.instructionItems);
    } else if (trainingData.instruction) {
      // Nếu không có instructionItems nhưng có instruction, lưu instruction
      await saveChatbotInstruction(chatbotId, trainingData.instruction);
    }

    // Lưu tính năng chatbot
    if (trainingData.features && trainingData.features.length > 0) {
      await saveChatbotFeatures(chatbotId, trainingData.features);
    }

    const successMessage = chatbotId
      ? trainingData.status === 'trained'
        ? 'Chatbot đã được cập nhật và huấn luyện thành công!'
        : 'Chatbot đã được cập nhật thành công!'
      : trainingData.status === 'trained'
        ? 'Chatbot đã được tạo và huấn luyện thành công!'
        : 'Chatbot đã được tạo thành công!';

    return {
      success: true,
      data: { id: chatbotId, ...chatbotData },
      message: successMessage,
    };
  } catch (error) {
    console.error('Error saving chatbot training data:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lưu FAQs cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} infoItems - Danh sách các mục thông tin
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function saveChatbotFAQs(chatbotId, infoItems) {
  try {
    if (!chatbotId) {
      return { success: false, error: 'Chatbot ID is required', data: null };
    }

    // Xóa tất cả FAQs hiện có của chatbot
    await deleteData(FAQ_TABLE, { chatbot_id: chatbotId }, true);

    // Tạo FAQs mới từ infoItems
    const faqPromises = infoItems.map((item, index) =>
      createData(FAQ_TABLE, {
        chatbot_id: chatbotId,
        question: item.title,
        answer: item.content,
        is_active: true,
        sort_order: index,
      })
    );

    await Promise.all(faqPromises);

    return { success: true, data: infoItems, message: 'FAQs đã được lưu thành công!' };
  } catch (error) {
    console.error('Error saving chatbot FAQs:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lưu instruction cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {string} instruction - Nội dung instruction
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function saveChatbotInstruction(chatbotId, instruction) {
  try {
    if (!chatbotId) {
      return { success: false, error: 'Chatbot ID is required', data: null };
    }

    // Xóa tất cả instructions hiện có của chatbot
    await deleteData(INSTRUCTION_TABLE, { chatbot_id: chatbotId }, true);

    // Tạo instruction mới
    const result = await createData(INSTRUCTION_TABLE, {
      chatbot_id: chatbotId,
      content: instruction,
      sort_order: 0,
    });

    return {
      success: result.success,
      data: result.data,
      message: 'Instruction đã được lưu thành công!',
    };
  } catch (error) {
    console.error('Error saving chatbot instruction:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lưu instruction items cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} instructionItems - Danh sách các mục hướng dẫn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function saveChatbotInstructionItems(chatbotId, instructionItems) {
  try {
    if (!chatbotId) {
      return { success: false, error: 'Chatbot ID is required', data: null };
    }

    // Xóa tất cả instructions hiện có của chatbot
    await deleteData(INSTRUCTION_TABLE, { chatbot_id: chatbotId }, true);

    // Tạo instructions mới từ instructionItems
    const instructionPromises = instructionItems.map((item, index) =>
      createData(INSTRUCTION_TABLE, {
        chatbot_id: chatbotId,
        title: item.title,
        content: item.content,
        sort_order: index,
      })
    );

    await Promise.all(instructionPromises);

    return {
      success: true,
      data: instructionItems,
      message: 'Instructions đã được lưu thành công!',
    };
  } catch (error) {
    console.error('Error saving chatbot instructions:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lưu tính năng cho chatbot
 * @param {string} chatbotId - ID của chatbot
 * @param {Array} features - Danh sách các tính năng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function saveChatbotFeatures(chatbotId, features) {
  try {
    if (!chatbotId) {
      return { success: false, error: 'Chatbot ID is required', data: null };
    }

    // Xóa tất cả features hiện có của chatbot
    await deleteData(CHATBOT_FEATURES_TABLE, { chatbot_id: chatbotId }, true);

    // Tạo features mới
    const featurePromises = features.map((featureId, index) =>
      createData(CHATBOT_FEATURES_TABLE, {
        chatbot_id: chatbotId,
        feature_id: featureId,
        is_active: true,
        sort_order: index,
      })
    );

    await Promise.all(featurePromises);

    return { success: true, data: features, message: 'Tính năng đã được lưu thành công!' };
  } catch (error) {
    console.error('Error saving chatbot features:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy dữ liệu training của chatbot
 * @param {string} chatbotId - ID của chatbot
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChatbotTrainingData(chatbotId) {
  try {
    if (!chatbotId) {
      return { success: false, error: 'Chatbot ID is required', data: null };
    }

    // Lấy thông tin chatbot
    const chatbotResult = await getChatbotById(chatbotId);
    if (!chatbotResult.success) {
      return chatbotResult;
    }

    const chatbot = chatbotResult.data;

    // Lấy FAQs
    let faqsResult;
    try {
      faqsResult = await fetchData(FAQ_TABLE, {
        filters: { chatbot_id: chatbotId },
        orderBy: 'sort_order',
        ascending: true,
      });

      // Nếu không có dữ liệu, tạo một kết quả trống nhưng thành công
      if (!faqsResult || faqsResult.error) {
        faqsResult = { success: true, data: [], error: null };
      }
    } catch (error) {
      console.error('Error fetching FAQs:', error);
      faqsResult = { success: true, data: [], error: null };
    }

    // Lấy instructions
    let instructionsResult;
    try {
      instructionsResult = await fetchData(INSTRUCTION_TABLE, {
        filters: { chatbot_id: chatbotId },
        orderBy: 'sort_order',
        ascending: true,
      });

      // Nếu không có dữ liệu, tạo một kết quả trống nhưng thành công
      if (!instructionsResult || instructionsResult.error) {
        instructionsResult = { success: true, data: [], error: null };
      }
    } catch (error) {
      console.error('Error fetching instructions:', error);
      instructionsResult = { success: true, data: [], error: null };
    }

    // Lấy features
    let featuresResult;
    try {
      featuresResult = await fetchData(CHATBOT_FEATURES_TABLE, {
        filters: { chatbot_id: chatbotId },
        orderBy: 'sort_order',
        ascending: true,
      });

      // Nếu không có dữ liệu, tạo một kết quả trống nhưng thành công
      if (!featuresResult || featuresResult.error) {
        featuresResult = { success: true, data: [], error: null };
      }
    } catch (error) {
      console.error('Error fetching features:', error);
      featuresResult = { success: true, data: [], error: null };
    }

    // Chuyển đổi FAQs thành infoItems
    const infoItems = faqsResult.success
      ? faqsResult.data.map((faq) => ({
          title: faq.question,
          content: faq.answer,
        }))
      : [];

    // Chuyển đổi instructions thành instructionItems
    const instructionItems = instructionsResult.success
      ? instructionsResult.data.map((instruction) => ({
          title: instruction.title || '',
          content: instruction.content || '',
        }))
      : [];

    // Lấy danh sách feature_id
    const features = featuresResult.success
      ? featuresResult.data.map((feature) => feature.feature_id)
      : ['product_consulting']; // Mặc định là tư vấn sản phẩm

    // Tạo dữ liệu training
    const trainingData = {
      id: chatbot.id,
      name: chatbot.name,
      avatarUrl: chatbot.avatarUrl,
      instruction: chatbot.instruction || '',
      infoItems,
      instructionItems,
      features,
      status: chatbot.status || 'draft',
    };

    return { success: true, data: trainingData };
  } catch (error) {
    console.error('Error getting chatbot training data:', error);
    return { success: false, error, data: null };
  }
}
