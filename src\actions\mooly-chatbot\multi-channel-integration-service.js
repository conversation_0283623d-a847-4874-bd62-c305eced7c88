'use client';

import { fetchData, createData, updateData } from './supabase-utils';

/**
 * Multi-channel Integration Service
 * Tích hợp và đồng bộ dữ liệu với các platform bán hàng khác
 */

// =====================================================
// 1. SUPPORTED CHANNELS
// =====================================================

export const CHANNEL_TYPES = {
  SHOPEE: 'shopee',
  LAZADA: 'lazada',
  TIKI: 'tiki',
  SENDO: 'sendo',
  FACEBOOK: 'facebook',
  INSTAGRAM: 'instagram',
  ZALO: 'zalo',
  WEBSITE: 'website',
  OFFLINE_STORE: 'offline_store'
};

export const SYNC_TYPES = {
  PRODUCTS: 'products',
  INVENTORY: 'inventory',
  ORDERS: 'orders',
  CUSTOMERS: 'customers',
  PRICING: 'pricing'
};

export const SYNC_DIRECTIONS = {
  IMPORT: 'import', // Từ channel về hệ thống
  EXPORT: 'export', // Từ hệ thống ra channel
  BIDIRECTIONAL: 'bidirectional' // Đồng bộ 2 chiều
};

// =====================================================
// 2. CHANNEL CONFIGURATION MANAGEMENT
// =====================================================

/**
 * Thêm channel integration mới
 */
export async function addChannelIntegration(channelData) {
  try {
    const integration = {
      channel_type: channelData.channelType,
      channel_name: channelData.channelName,
      api_credentials: channelData.apiCredentials || {},
      sync_settings: channelData.syncSettings || {},
      is_active: channelData.isActive !== false,
      last_sync_at: null,
      sync_status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const result = await createData('channel_integrations', integration);

    if (result.success) {
      // Test connection
      await testChannelConnection(result.data.id);
    }

    return result;
  } catch (error) {
    console.error('Error adding channel integration:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Lấy danh sách channel integrations
 */
export async function getChannelIntegrations(filters = {}) {
  try {
    let query = 'channel_integrations';
    const conditions = [];

    if (filters.channelType) {
      conditions.push(`channel_type.eq.${filters.channelType}`);
    }

    if (filters.isActive !== undefined) {
      conditions.push(`is_active.eq.${filters.isActive}`);
    }

    if (conditions.length > 0) {
      query += `?${conditions.join('&')}`;
    }

    const result = await fetchData(query);
    return result;
  } catch (error) {
    console.error('Error fetching channel integrations:', error);
    return {
      success: false,
      error: error.message,
      data: []
    };
  }
}

/**
 * Cập nhật channel integration
 */
export async function updateChannelIntegration(integrationId, updates) {
  try {
    const updateDataPayload = {
      ...updates,
      updated_at: new Date().toISOString()
    };

    const result = await updateData('channel_integrations', updateDataPayload, { id: integrationId });
    return result;
  } catch (error) {
    console.error('Error updating channel integration:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test kết nối với channel
 */
export async function testChannelConnection(integrationId) {
  try {
    const integrationResult = await fetchData(`channel_integrations?id=eq.${integrationId}`);

    if (!integrationResult.success || !integrationResult.data.length) {
      throw new Error('Integration not found');
    }

    const integration = integrationResult.data[0];
    const testResult = await performConnectionTest(integration);

    // Cập nhật trạng thái
    await updateChannelIntegration(integrationId, {
      sync_status: testResult.success ? 'connected' : 'error',
      last_sync_at: new Date().toISOString(),
      sync_error: testResult.success ? null : testResult.error
    });

    return testResult;
  } catch (error) {
    console.error('Error testing channel connection:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// =====================================================
// 3. DATA SYNCHRONIZATION
// =====================================================

/**
 * Đồng bộ dữ liệu với channel
 */
export async function syncChannelData(integrationId, syncType, direction = SYNC_DIRECTIONS.BIDIRECTIONAL) {
  try {
    const integrationResult = await fetchData(`channel_integrations?id=eq.${integrationId}`);

    if (!integrationResult.success || !integrationResult.data.length) {
      throw new Error('Integration not found');
    }

    const integration = integrationResult.data[0];

    // Kiểm tra trạng thái kết nối
    if (integration.sync_status !== 'connected') {
      throw new Error('Channel not connected');
    }

    let syncResult;

    switch (syncType) {
      case SYNC_TYPES.PRODUCTS:
        syncResult = await syncProducts(integration, direction);
        break;
      case SYNC_TYPES.INVENTORY:
        syncResult = await syncInventory(integration, direction);
        break;
      case SYNC_TYPES.ORDERS:
        syncResult = await syncOrders(integration, direction);
        break;
      case SYNC_TYPES.CUSTOMERS:
        syncResult = await syncCustomers(integration, direction);
        break;
      case SYNC_TYPES.PRICING:
        syncResult = await syncPricing(integration, direction);
        break;
      default:
        throw new Error(`Unsupported sync type: ${syncType}`);
    }

    // Log sync activity
    await logSyncActivity({
      integration_id: integrationId,
      sync_type: syncType,
      direction,
      result: syncResult,
      synced_at: new Date().toISOString()
    });

    // Cập nhật last sync time
    await updateChannelIntegration(integrationId, {
      last_sync_at: new Date().toISOString(),
      sync_status: syncResult.success ? 'connected' : 'error',
      sync_error: syncResult.success ? null : syncResult.error
    });

    return syncResult;
  } catch (error) {
    console.error('Error syncing channel data:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Đồng bộ tất cả channels
 */
export async function syncAllChannels(syncType) {
  try {
    const integrationsResult = await getChannelIntegrations({ isActive: true });

    if (!integrationsResult.success) {
      throw new Error('Failed to fetch integrations');
    }

    const syncResults = [];

    for (const integration of integrationsResult.data) {
      try {
        const result = await syncChannelData(integration.id, syncType);
        syncResults.push({
          integrationId: integration.id,
          channelName: integration.channel_name,
          result
        });
      } catch (error) {
        syncResults.push({
          integrationId: integration.id,
          channelName: integration.channel_name,
          result: {
            success: false,
            error: error.message
          }
        });
      }
    }

    return {
      success: true,
      results: syncResults,
      totalChannels: integrationsResult.data.length,
      successfulSyncs: syncResults.filter(r => r.result.success).length
    };
  } catch (error) {
    console.error('Error syncing all channels:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// =====================================================
// 4. SPECIFIC SYNC IMPLEMENTATIONS
// =====================================================

/**
 * Đồng bộ sản phẩm
 */
async function syncProducts(integration, direction) {
  try {
    const channelAdapter = getChannelAdapter(integration.channel_type);

    if (direction === SYNC_DIRECTIONS.EXPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Export products to channel
      const productsResult = await fetchData('products?is_active=eq.true');

      if (productsResult.success) {
        const exportResult = await channelAdapter.exportProducts(
          productsResult.data,
          integration.api_credentials
        );

        if (!exportResult.success) {
          throw new Error(`Export failed: ${exportResult.error}`);
        }
      }
    }

    if (direction === SYNC_DIRECTIONS.IMPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Import products from channel
      const importResult = await channelAdapter.importProducts(integration.api_credentials);

      if (importResult.success && importResult.data.length > 0) {
        // Process imported products
        await processImportedProducts(importResult.data, integration.id);
      }
    }

    return {
      success: true,
      message: 'Products synced successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Đồng bộ tồn kho
 */
async function syncInventory(integration, direction) {
  try {
    const channelAdapter = getChannelAdapter(integration.channel_type);

    if (direction === SYNC_DIRECTIONS.EXPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Export inventory to channel
      const inventoryResult = await fetchData('products?select=id,sku,inventory_quantity');

      if (inventoryResult.success) {
        const exportResult = await channelAdapter.exportInventory(
          inventoryResult.data,
          integration.api_credentials
        );

        if (!exportResult.success) {
          throw new Error(`Inventory export failed: ${exportResult.error}`);
        }
      }
    }

    return {
      success: true,
      message: 'Inventory synced successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Đồng bộ đơn hàng
 */
async function syncOrders(integration, direction) {
  try {
    const channelAdapter = getChannelAdapter(integration.channel_type);

    if (direction === SYNC_DIRECTIONS.IMPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Import orders from channel
      const importResult = await channelAdapter.importOrders(integration.api_credentials);

      if (importResult.success && importResult.data.length > 0) {
        // Process imported orders
        await processImportedOrders(importResult.data, integration.id);
      }
    }

    return {
      success: true,
      message: 'Orders synced successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Đồng bộ khách hàng
 */
async function syncCustomers(integration, direction) {
  try {
    const channelAdapter = getChannelAdapter(integration.channel_type);

    if (direction === SYNC_DIRECTIONS.IMPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Import customers from channel
      const importResult = await channelAdapter.importCustomers(integration.api_credentials);

      if (importResult.success && importResult.data.length > 0) {
        // Process imported customers
        await processImportedCustomers(importResult.data, integration.id);
      }
    }

    return {
      success: true,
      message: 'Customers synced successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Đồng bộ giá
 */
async function syncPricing(integration, direction) {
  try {
    const channelAdapter = getChannelAdapter(integration.channel_type);

    if (direction === SYNC_DIRECTIONS.EXPORT || direction === SYNC_DIRECTIONS.BIDIRECTIONAL) {
      // Export pricing to channel
      const pricingResult = await fetchData('products?select=id,sku,selling_price');

      if (pricingResult.success) {
        const exportResult = await channelAdapter.exportPricing(
          pricingResult.data,
          integration.api_credentials
        );

        if (!exportResult.success) {
          throw new Error(`Pricing export failed: ${exportResult.error}`);
        }
      }
    }

    return {
      success: true,
      message: 'Pricing synced successfully'
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// =====================================================
// 5. CHANNEL ADAPTERS
// =====================================================

/**
 * Lấy adapter cho channel cụ thể
 */
function getChannelAdapter(channelType) {
  const adapters = {
    [CHANNEL_TYPES.SHOPEE]: new ShopeeAdapter(),
    [CHANNEL_TYPES.LAZADA]: new LazadaAdapter(),
    [CHANNEL_TYPES.TIKI]: new TikiAdapter(),
    [CHANNEL_TYPES.SENDO]: new SendoAdapter(),
    [CHANNEL_TYPES.FACEBOOK]: new FacebookAdapter(),
    [CHANNEL_TYPES.WEBSITE]: new WebsiteAdapter()
  };

  const adapter = adapters[channelType];
  if (!adapter) {
    throw new Error(`Unsupported channel type: ${channelType}`);
  }

  return adapter;
}

// Base adapter class
class BaseChannelAdapter {
  async testConnection(credentials) {
    throw new Error('testConnection method must be implemented');
  }

  async exportProducts(products, credentials) {
    throw new Error('exportProducts method must be implemented');
  }

  async importProducts(credentials) {
    throw new Error('importProducts method must be implemented');
  }

  async exportInventory(inventory, credentials) {
    throw new Error('exportInventory method must be implemented');
  }

  async importOrders(credentials) {
    throw new Error('importOrders method must be implemented');
  }

  async importCustomers(credentials) {
    throw new Error('importCustomers method must be implemented');
  }

  async exportPricing(pricing, credentials) {
    throw new Error('exportPricing method must be implemented');
  }
}

// Shopee adapter implementation
class ShopeeAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    // Implementation for Shopee API connection test
    return { success: true, message: 'Shopee connection successful' };
  }

  async exportProducts(products, credentials) {
    // Implementation for exporting products to Shopee
    return { success: true, exported: products.length };
  }

  async importProducts(credentials) {
    // Implementation for importing products from Shopee
    return { success: true, data: [] };
  }

  async exportInventory(inventory, credentials) {
    // Implementation for exporting inventory to Shopee
    return { success: true, updated: inventory.length };
  }

  async importOrders(credentials) {
    // Implementation for importing orders from Shopee
    return { success: true, data: [] };
  }

  async importCustomers(credentials) {
    // Implementation for importing customers from Shopee
    return { success: true, data: [] };
  }

  async exportPricing(pricing, credentials) {
    // Implementation for exporting pricing to Shopee
    return { success: true, updated: pricing.length };
  }
}

// Placeholder implementations for other adapters
class LazadaAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    return { success: true, message: 'Lazada connection successful' };
  }
}

class TikiAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    return { success: true, message: 'Tiki connection successful' };
  }
}

class SendoAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    return { success: true, message: 'Sendo connection successful' };
  }
}

class FacebookAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    return { success: true, message: 'Facebook connection successful' };
  }
}

class WebsiteAdapter extends BaseChannelAdapter {
  async testConnection(credentials) {
    return { success: true, message: 'Website connection successful' };
  }
}

// =====================================================
// 6. UTILITY FUNCTIONS
// =====================================================

async function performConnectionTest(integration) {
  try {
    const adapter = getChannelAdapter(integration.channel_type);
    return await adapter.testConnection(integration.api_credentials);
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function processImportedProducts(products, integrationId) {
  // Process and save imported products
  for (const product of products) {
    try {
      await createData('products', {
        ...product,
        source_integration_id: integrationId,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error processing imported product:', error);
    }
  }
}

async function processImportedOrders(orders, integrationId) {
  // Process and save imported orders
  for (const order of orders) {
    try {
      await createData('orders', {
        ...order,
        source_integration_id: integrationId,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error processing imported order:', error);
    }
  }
}

async function processImportedCustomers(customers, integrationId) {
  // Process and save imported customers
  for (const customer of customers) {
    try {
      await createData('customers', {
        ...customer,
        source_integration_id: integrationId,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error processing imported customer:', error);
    }
  }
}

async function logSyncActivity(activity) {
  try {
    await createData('sync_logs', activity);
  } catch (error) {
    console.error('Error logging sync activity:', error);
  }
}
