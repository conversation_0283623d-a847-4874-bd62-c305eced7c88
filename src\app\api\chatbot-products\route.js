import { NextResponse } from 'next/server';

import { createClient } from 'src/utils/supabase/server';
import { withTenantAuth } from 'src/utils/server-auth';

/**
 * API route để thêm sản phẩm vào chatbot
 * @param {Request} request - <PERSON><PERSON><PERSON> cầu HTTP với body: { chatbot_id, product_ids }
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const POST = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { chatbot_id, product_ids } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!chatbot_id || !product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: chatbot_id, product_ids (array)' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // <PERSON><PERSON><PERSON> thông tin sản phẩm từ database
    const { data: products, error: productError } = await supabase
      .from('products')
      .select('id, name, price, compare_at_price, stock_quantity, description, avatar')
      .in('id', product_ids)
      .eq('tenant_id', tenantId)
      .eq('is_active', true);

    if (productError) {
      console.error('Error fetching products:', productError);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }

    if (!products || products.length === 0) {
      return NextResponse.json({ error: 'No valid products found' }, { status: 404 });
    }

    // Chuẩn bị dữ liệu để thêm vào bảng chatbot_products
    const chatbotProductsData = products.map((product) => ({
      chatbot_id,
      product_id: product.id,
      tenant_id: tenantId,
    }));

    // Thêm vào bảng chatbot_products (sử dụng upsert để tránh duplicate)
    const { error: insertError } = await supabase
      .from('chatbot_products')
      .upsert(chatbotProductsData, {
        onConflict: 'chatbot_id, product_id',
        ignoreDuplicates: true,
      });

    if (insertError) {
      console.error('Error inserting chatbot products:', insertError);
      return NextResponse.json({ error: 'Failed to add products to chatbot' }, { status: 500 });
    }

    // Chuẩn bị dữ liệu để đồng bộ với Weaviate
    const weaviateProducts = products.map((product) => ({
      name: product.name,
      price: product.price,
      compare_at_price: product.compare_at_price,
      stock: product.stock_quantity,
      description: product.description,
      image_url: product.avatar,
      tenant_id: tenantId,
      bot_id: chatbot_id,
      product_id: product.id,
    }));

    // Đồng bộ với Weaviate sử dụng batch API
    try {
      const weaviateResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/weaviate/products/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ products: weaviateProducts }),
      });

      if (!weaviateResponse.ok) {
        console.error('Failed to sync with Weaviate');
        // Không throw error vì dữ liệu đã được lưu vào database
      }
    } catch (weaviateError) {
      console.error('Error syncing with Weaviate:', weaviateError);
      // Không throw error vì dữ liệu đã được lưu vào database
    }

    return NextResponse.json({ 
      message: 'Products added to chatbot successfully', 
      count: products.length 
    });
  } catch (error) {
    console.error('Error in chatbot products POST:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});

/**
 * API route để xóa sản phẩm khỏi chatbot
 * @param {Request} request - Yêu cầu HTTP với body: { chatbot_id, product_ids }
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const DELETE = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { chatbot_id, product_ids } = await request.json();

    // Kiểm tra dữ liệu đầu vào
    if (!chatbot_id || !product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: chatbot_id, product_ids (array)' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Xóa khỏi bảng chatbot_products
    const { error: deleteError } = await supabase
      .from('chatbot_products')
      .delete()
      .eq('chatbot_id', chatbot_id)
      .in('product_id', product_ids)
      .eq('tenant_id', tenantId);

    if (deleteError) {
      console.error('Error deleting chatbot products:', deleteError);
      return NextResponse.json({ error: 'Failed to remove products from chatbot' }, { status: 500 });
    }

    // Đồng bộ với Weaviate - xóa sản phẩm
    try {
      const weaviateResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/weaviate/products/delete-by-ids`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          product_ids,
          tenant_id: tenantId 
        }),
      });

      if (!weaviateResponse.ok) {
        console.error('Failed to sync deletion with Weaviate');
        // Không throw error vì dữ liệu đã được xóa khỏi database
      }
    } catch (weaviateError) {
      console.error('Error syncing deletion with Weaviate:', weaviateError);
      // Không throw error vì dữ liệu đã được xóa khỏi database
    }

    return NextResponse.json({ 
      message: 'Products removed from chatbot successfully',
      count: product_ids.length
    });
  } catch (error) {
    console.error('Error in chatbot products DELETE:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
});

/**
 * API route để lấy danh sách sản phẩm của chatbot
 * @param {Request} request - Yêu cầu HTTP với query: ?chatbot_id=xxx
 * @returns {Promise<NextResponse>} - Phản hồi HTTP
 */
export const GET = withTenantAuth(async (request, { tenantId, userId }) => {
  try {
    const { searchParams } = new URL(request.url);
    const chatbot_id = searchParams.get('chatbot_id');

    if (!chatbot_id) {
      return NextResponse.json(
        { error: 'Missing required query parameter: chatbot_id' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Lấy danh sách sản phẩm của chatbot
    const { data: chatbotProducts, error } = await supabase
      .from('chatbot_products')
      .select(`
        product_id,
        created_at,
        products (
          id,
          name,
          price,
          compare_at_price,
          stock_quantity,
          description,
          avatar,
          sku,
          is_active
        )
      `)
      .eq('chatbot_id', chatbot_id)
      .eq('tenant_id', tenantId);

    if (error) {
      console.error('Error fetching chatbot products:', error);
      return NextResponse.json({ error: 'Failed to fetch chatbot products' }, { status: 500 });
    }

    // Chuyển đổi dữ liệu để trả về
    const products = chatbotProducts.map((item) => ({
      ...item.products,
      added_to_chatbot_at: item.created_at,
    }));

    return NextResponse.json({ products });
  } catch (error) {
    console.error('Error in chatbot products GET:', error);
    return NextResponse.json({ error: error.message || 'Internal server error' }, { status: 500 });
  }
}); 