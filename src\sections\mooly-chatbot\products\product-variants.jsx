'use client';

import PropTypes from 'prop-types';
import React, { useMemo } from 'react';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';

import { FORM_FIELDS, PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { Iconify } from 'src/components/iconify';

import useVariantManager from './hooks/useVariantManager';
import useAttributeManager from './hooks/useAttributeManager';
import {
  VariantTable,
  BulkEditMenu,
  AttributeForm,
  AttributeList,
  ImageSelector,
  EditAttributeDialog,
} from './components';

// ----------------------------------------------------------------------

/**
 * Chuyển đổi attributes từ object format sang array format
 * @param {Object|Array} attributes - Thuộc tính ở dạng object hoặc array
 * @returns {Array} - Thuộc tính ở dạng array
 */
function normalizeAttributes(attributes) {
  // Nếu đã là array, trả về nguyên vẹn
  if (Array.isArray(attributes)) {
    return attributes;
  }

  // Nếu là object, chuyển đổi sang array format
  if (attributes && typeof attributes === 'object') {
    return Object.entries(attributes).map(([name, values]) => ({
      name,
      values: Array.isArray(values) ? values : [],
    }));
  }

  // Trường hợp khác, trả về array rỗng
  return [];
}

export default function ProductVariants({ watch, setValue, trigger, isEditMode = false, canEditAttributes = true }) {
  const productType = watch(FORM_FIELDS.PRODUCT_TYPE);

  // Tối ưu: Sử dụng useMemo để tránh tạo mảng mới mỗi lần render và normalize attributes
  const attributes = useMemo(() => {
    const rawAttributes = watch('attributes');
    return normalizeAttributes(rawAttributes);
  }, [watch]);

  // Tối ưu: Sử dụng custom hooks để quản lý biến thể và thuộc tính với edit mode support
  const variantManager = useVariantManager(watch, setValue, isEditMode, trigger);
  const attributeManager = useAttributeManager(watch, setValue, variantManager.generateVariants, isEditMode, canEditAttributes);

  // Chỉ hiển thị khi là sản phẩm có biến thể
  if (productType !== PRODUCT_TYPES.VARIABLE) {
    return null;
  }

  return (
    <Card>
      <CardHeader
        title={isEditMode ? "Danh sách biến thể" : "Biến thể sản phẩm"}
        subheader={isEditMode ? "Chỉnh sửa thông tin các biến thể hiện có" : undefined}
      />

      {/* Menu cập nhật hàng loạt */}
      <BulkEditMenu
        anchorEl={variantManager.anchorEl}
        onClose={variantManager.handleBulkMenuClose}
        bulkEditField={variantManager.bulkEditField}
        setBulkEditField={variantManager.setBulkEditField}
        bulkEditValue={variantManager.bulkEditValue}
        setBulkEditValue={variantManager.setBulkEditValue}
        onBulkEdit={variantManager.handleBulkEdit}
        isEditMode={isEditMode}
      />

      <CardContent>
        <Stack spacing={3}>
          {/* Tối ưu UI/UX: Ẩn hoàn toàn form thuộc tính trong edit mode */}
          {!isEditMode && (
            <>
              <AttributeForm
                attributeName={attributeManager.attributeName}
                setAttributeName={attributeManager.setAttributeName}
                attributeValue={attributeManager.attributeValue}
                setAttributeValue={attributeManager.setAttributeValue}
                attributeValues={attributeManager.attributeValues}
                handleAddAttributeValue={attributeManager.handleAddAttributeValue}
                handleRemoveAttributeValue={attributeManager.handleRemoveAttributeValue}
                handleAddAttribute={attributeManager.handleAddAttribute}
                isEditMode={isEditMode}
                canEditAttributes={canEditAttributes}
                existingAttributes={attributes}
              />

              {/* Danh sách thuộc tính đã thêm - chỉ hiển thị khi tạo mới */}
              <Divider />
              <AttributeList
                attributes={attributes}
                handleEditAttribute={attributeManager.handleEditAttribute}
                handleRemoveAttribute={attributeManager.handleRemoveAttribute}
                isEditMode={isEditMode}
                canEditAttributes={canEditAttributes}
              />
            </>
          )}

          {/* Tối ưu UI/UX: Danh sách biến thể - luôn hiển thị */}
          {(() => {
            const formVariants = watch('variants') || [];

            return formVariants.length > 0 ? (
              <>
                {/* Tối ưu UI/UX: Ẩn nút cập nhật hàng loạt trong edit mode để UI gọn hơn */}
                {!isEditMode && (
                  <>
                    <Button
                      variant="outlined"
                      startIcon={<Iconify icon="eva:edit-fill" />}
                      onClick={variantManager.handleBulkMenuOpen}
                    >
                      Cập nhật hàng loạt
                    </Button>
                    <Divider />
                  </>
                )}

                <VariantTable
                  variants={formVariants}
                  handleVariantChange={variantManager.handleVariantChange}
                  handleOpenImageSelect={variantManager.handleOpenImageSelect}
                  handleRemoveVariantImage={variantManager.handleRemoveVariantImage}
                  isEditMode={isEditMode}
                />
              </>
            ) : (
              // Tối ưu UI/UX: Hiển thị thông báo khi không có biến thể trong edit mode
              isEditMode && (
                <Alert severity="warning" variant="outlined">
                  Không có biến thể nào được tìm thấy cho sản phẩm này.
                </Alert>
              )
            );
          })()}
        </Stack>
      </CardContent>

      {/* Dialog chỉnh sửa thuộc tính - Ẩn trong chế độ edit */}
      {!isEditMode && (
        <EditAttributeDialog
          open={attributeManager.editDialogOpen}
          onClose={() => attributeManager.setEditDialogOpen(false)}
          attributeName={attributeManager.attributeName}
          setAttributeName={attributeManager.setAttributeName}
          attributeValue={attributeManager.attributeValue}
          setAttributeValue={attributeManager.setAttributeValue}
          attributeValues={attributeManager.attributeValues}
          handleAddAttributeValue={attributeManager.handleAddAttributeValue}
          handleRemoveAttributeValue={attributeManager.handleRemoveAttributeValue}
          handleSaveEditAttribute={attributeManager.handleSaveEditAttribute}
        />
      )}

      {/* Dialog chọn ảnh cho biến thể */}
      <ImageSelector
        open={variantManager.imageSelectOpen}
        media={watch('images') || []}
        onClose={() => variantManager.setImageSelectOpen(false)}
        onSelectImage={variantManager.handleSelectVariantImage}
        selectedImage={variantManager.getSelectedVariantImage()}
      />
    </Card>
  );
}

ProductVariants.propTypes = {
  watch: PropTypes.func,
  setValue: PropTypes.func,
  trigger: PropTypes.func,
  isEditMode: PropTypes.bool,
  canEditAttributes: PropTypes.bool,
};
