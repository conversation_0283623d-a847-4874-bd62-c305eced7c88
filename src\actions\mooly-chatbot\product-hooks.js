'use client';

import { useRef, useState, useEffect } from 'react';

import { getProducts } from './product-api';
import { fetchData } from './supabase-utils';
import { DEFAULT_PRODUCT_OPTIONS } from './product-constants';
import { getProductVariants } from './product-variant-service';

/**
 * Hook để lấy danh sách sản phẩm - Tối ưu để luôn load dữ liệu mới nhất
 * @param {Object} options - C<PERSON><PERSON> tùy chọn
 * @returns {Object} - Kết quả từ API
 */
export function useProducts(options = {}) {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Tối ưu: Luôn sử dụng options mới nhất, không cache
  const fetchProducts = async (freshOptions = options) => {
    setIsValidating(true);
    setError(null);
    try {
      // Merge với default options và đảm bảo luôn fresh
      const currentOptions = {
        ...DEFAULT_PRODUCT_OPTIONS,
        ...freshOptions,
        // Tối ưu: Luôn force fresh data, không cache
        timestamp: Date.now()
      };

      // Ensure we get count for pagination
      const countEnabled = currentOptions.count !== false;
      if (countEnabled) {
        currentOptions.count = true;
      }

      const result = await getProducts(currentOptions);

      if (result.success) {
        setProducts(result.data || []);
        if (countEnabled) {
          setTotalCount(result.count || 0);
        }
      } else {
        setError(result.error);
      }

      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  };

  // Hàm để tải lại dữ liệu - luôn fresh
  const mutate = async (freshOptions) => fetchProducts(freshOptions || options);

  // Tải dữ liệu khi component mount
  useEffect(() => {
    setIsLoading(true);
    fetchProducts(options);
  }, []);

  // Tối ưu: Reload data khi options thay đổi, luôn fresh
  useEffect(() => {
    setIsLoading(true);
    fetchProducts(options);
  }, [JSON.stringify(options)]); // Deep comparison để detect changes

  return {
    products,
    isLoading,
    isError: !!error,
    error,
    isValidating,
    totalCount,
    mutate,
    isEmpty: !isLoading && !isValidating && (!products || products.length === 0),
  };
}

/**
 * Hook để lấy thông tin chi tiết sản phẩm - Tối ưu cho fresh data
 * @param {string} productId - ID sản phẩm cần lấy
 * @param {number} timestamp - Timestamp để đảm bảo fresh data
 * @returns {Promise<Object>} - Kết quả từ API
 */
const getProductDetail = async (productId, timestamp = Date.now()) => {
  if (!productId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  // Tối ưu: Sử dụng fetchData với timestamp để đảm bảo fresh data
  return fetchData('products', {
    filters: { id: productId },
    columns: '*, category_id(id, name)',
    single: true,
    // Tối ưu: Thêm timestamp để force fresh data
    _timestamp: timestamp,
  });
};

export function useProduct(productId) {
  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);

  // Tối ưu: Luôn fetch fresh data, không cache
  const fetchProduct = async (forceRefresh = false) => {
    if (!productId) {
      setProduct(null);
      return;
    }

    setIsValidating(true);
    setError(null);
    try {
      // Tối ưu: Thêm timestamp để đảm bảo fresh data
      const timestamp = Date.now();

      // Sử dụng Promise.all để thực hiện các truy vấn song song với fresh data
      const [productResult, variantsResult] = await Promise.all([
        // Lấy thông tin sản phẩm với fresh data
        getProductDetail(productId, timestamp),
        // Lấy thông tin biến thể của sản phẩm với fresh data
        getProductVariants(productId, timestamp),
      ]);

      if (productResult.success && productResult.data) {
        const productData = productResult.data;

        // Tối ưu: Xử lý attributes để đảm bảo format đúng cho edit mode
        let processedAttributes = [];
        if (productData.attributes) {
          if (Array.isArray(productData.attributes)) {
            processedAttributes = productData.attributes;
          } else if (typeof productData.attributes === 'object') {
            // Convert object format to array format
            processedAttributes = Object.entries(productData.attributes).map(([name, values]) => ({
              name,
              values: Array.isArray(values) ? values : [],
            }));
          }
        }

        // Gộp dữ liệu với xử lý chính xác
        const fullProduct = {
          ...productData,
          categoryId: productData.categoryId?.id || productData.categoryId || null,
          metaKeywords: productData.metaKeywords || [],
          images: productData.images || [],
          attributes: processedAttributes,
          variants: variantsResult.success ? (variantsResult.data || []) : [],
          // Tối ưu: Đảm bảo các field cần thiết có giá trị mặc định
          tags: productData.tags || [],
          gender: productData.gender || [],
          saleLabel: productData.saleLabel || { enabled: false, content: '' },
          newLabel: productData.newLabel || { enabled: false, content: '' },
          seoTitle: productData.seoTitle || '',
          seoDescription: productData.seoDescription || '',
          description: productData.description || '',
          shortDescription: productData.shortDescription || '',
          url: productData.url || '',
          slug: productData.slug || '',
          sku: productData.sku || '',
        };

        setProduct(fullProduct);
      } else {
        setError(productResult.error || new Error('Không tìm thấy sản phẩm'));
      }
    } catch (err) {
      setError(err);
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (productId) {
      setIsLoading(true);
      fetchProduct();
    } else {
      setProduct(null);
    }
  }, [productId]);

  return {
    product,
    isLoading,
    isError: !!error,
    error,
    isValidating,
    refetch: () => fetchProduct(true), // Force refresh
  };
}
