/**
 * Utility functions để xử lý giá sản phẩm một cách nhất quán
 * Hỗ trợ cấu trúc giá mới: price (gi<PERSON> bán) và compareAtPrice (giá so sánh)
 */

/**
 * Normalize giá sản phẩm từ database
 * @param {Object} data - Dữ liệu sản phẩm từ database
 * @returns {Object} - Object chứa price và compareAtPrice đã chuẩn hóa
 */
export function normalizeProductPrice(data) {
  if (!data) return { price: 0, compareAtPrice: null };

  // Xử lý price - ưu tiên price mới, fallback về sellingPrice/selling_price cũ
  const price = data.price !== null && data.price !== undefined
    ? Number(data.price)
    : (data.sellingPrice !== null && data.sellingPrice !== undefined 
      ? Number(data.sellingPrice) 
      : (data.selling_price !== null && data.selling_price !== undefined 
        ? Number(data.selling_price) 
        : 0));

  // Xử lý compareAtPrice - <PERSON>u tiên compareAtPrice mới, fallback về compare_at_price
  const compareAtPrice = data.compareAtPrice !== null && data.compareAtPrice !== undefined
    ? Number(data.compareAtPrice)
    : (data.compare_at_price !== null && data.compare_at_price !== undefined 
      ? Number(data.compare_at_price) 
      : null);

  return { 
    price: Math.max(0, price), // Đảm bảo giá không âm
    compareAtPrice: compareAtPrice && compareAtPrice > 0 ? compareAtPrice : null 
  };
}

/**
 * Lấy giá hiển thị chính (giá bán thực tế)
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {number} - Giá hiển thị
 */
export function getDisplayPrice(data) {
  if (!data) return 0;
  const { price } = normalizeProductPrice(data);
  return price;
}

/**
 * Lấy giá so sánh (giá gốc)
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {number|null} - Giá so sánh hoặc null nếu không có
 */
export function getCompareAtPrice(data) {
  if (!data) return null;
  const { compareAtPrice } = normalizeProductPrice(data);
  return compareAtPrice;
}

/**
 * Kiểm tra xem sản phẩm có đang giảm giá không
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {boolean} - true nếu đang giảm giá
 */
export function isOnSale(data) {
  if (!data) return false;
  const { price, compareAtPrice } = normalizeProductPrice(data);
  return compareAtPrice && compareAtPrice > price;
}

/**
 * Tính phần trăm giảm giá
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {number} - Phần trăm giảm giá (0-100)
 */
export function getDiscountPercentage(data) {
  if (!data) return 0;
  const { price, compareAtPrice } = normalizeProductPrice(data);
  
  if (!compareAtPrice || compareAtPrice <= price) return 0;
  
  return Math.round(((compareAtPrice - price) / compareAtPrice) * 100);
}

/**
 * Tính số tiền tiết kiệm
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {number} - Số tiền tiết kiệm
 */
export function getSavingsAmount(data) {
  if (!data) return 0;
  const { price, compareAtPrice } = normalizeProductPrice(data);
  
  if (!compareAtPrice || compareAtPrice <= price) return 0;
  
  return compareAtPrice - price;
}

/**
 * Format giá để hiển thị với thông tin giảm giá
 * @param {Object} data - Dữ liệu sản phẩm
 * @param {Object} options - Tùy chọn format
 * @returns {Object} - Object chứa thông tin giá đã format
 */
export function formatPriceDisplay(data, options = {}) {
  const {
    showComparePrice = true,
    showDiscountPercentage = true,
    showSavingsAmount = false,
    currency = 'VND',
    locale = 'vi-VN'
  } = options;

  if (!data) {
    return {
      price: '0 ₫',
      compareAtPrice: null,
      discountPercentage: 0,
      savingsAmount: 0,
      isOnSale: false
    };
  }

  const { price, compareAtPrice } = normalizeProductPrice(data);
  const onSale = isOnSale(data);
  const discountPercentage = getDiscountPercentage(data);
  const savingsAmount = getSavingsAmount(data);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return {
    price: formatCurrency(price),
    compareAtPrice: showComparePrice && compareAtPrice ? formatCurrency(compareAtPrice) : null,
    discountPercentage: showDiscountPercentage ? discountPercentage : 0,
    savingsAmount: showSavingsAmount ? formatCurrency(savingsAmount) : 0,
    isOnSale: onSale,
    rawPrice: price,
    rawCompareAtPrice: compareAtPrice
  };
}

/**
 * Chuẩn hóa dữ liệu sản phẩm để lưu vào database
 * @param {Object} productData - Dữ liệu sản phẩm từ form
 * @returns {Object} - Dữ liệu đã chuẩn hóa cho database
 */
export function normalizeProductDataForDB(productData) {
  if (!productData) return {};

  const normalized = { ...productData };

  // Chuẩn hóa price
  if (normalized.price !== undefined) {
    normalized.price = Number(normalized.price) || 0;
  }

  // Chuẩn hóa compareAtPrice
  if (normalized.compareAtPrice !== undefined) {
    normalized.compareAtPrice = normalized.compareAtPrice && Number(normalized.compareAtPrice) > 0 
      ? Number(normalized.compareAtPrice) 
      : null;
  }

  // Xóa các field cũ nếu có
  delete normalized.sellingPrice;
  delete normalized.selling_price;

  return normalized;
}

/**
 * Chuẩn hóa dữ liệu variant để lưu vào database
 * @param {Object} variantData - Dữ liệu variant từ form
 * @returns {Object} - Dữ liệu đã chuẩn hóa cho database
 */
export function normalizeVariantDataForDB(variantData) {
  if (!variantData) return {};

  const normalized = { ...variantData };

  // Chuẩn hóa price
  if (normalized.price !== undefined) {
    normalized.price = Number(normalized.price) || 0;
  }

  // Chuẩn hóa compareAtPrice
  if (normalized.compareAtPrice !== undefined) {
    normalized.compareAtPrice = normalized.compareAtPrice && Number(normalized.compareAtPrice) > 0 
      ? Number(normalized.compareAtPrice) 
      : null;
  }

  // Xóa các field cũ nếu có
  delete normalized.sellingPrice;
  delete normalized.selling_price;

  return normalized;
}

/**
 * Validate giá sản phẩm
 * @param {Object} data - Dữ liệu sản phẩm
 * @returns {Object} - Kết quả validation
 */
export function validateProductPrice(data) {
  const errors = [];
  
  if (!data) {
    errors.push('Dữ liệu sản phẩm không hợp lệ');
    return { isValid: false, errors };
  }

  const { price, compareAtPrice } = normalizeProductPrice(data);

  // Kiểm tra price
  if (price < 0) {
    errors.push('Giá bán không được âm');
  }

  // Kiểm tra compareAtPrice
  if (compareAtPrice !== null && compareAtPrice < 0) {
    errors.push('Giá so sánh không được âm');
  }

  // Kiểm tra logic giá
  if (compareAtPrice !== null && compareAtPrice <= price) {
    errors.push('Giá so sánh phải lớn hơn giá bán');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
