import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';

/**
 * Component menu cập nhật hàng loạt biến thể
 */
export default function BulkEditMenu({
  anchorEl,
  onClose,
  bulkEditField,
  setBulkEditField,
  bulkEditValue,
  setBulkEditValue,
  onBulkEdit,
  isEditMode,
}) {
  return (
    <Menu
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <Box sx={{ p: 2, width: 300 }}>
        <Stack spacing={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Trường cập nhật</InputLabel>
            <Select
              value={bulkEditField}
              onChange={(e) => setBulkEditField(e.target.value)}
              label="Trường cập nhật"
              disabled={isEditMode && bulkEditField === 'stockQuantity'}
            >
              <MenuItem value="sku">SKU</MenuItem>
              <MenuItem value="sellingPrice">Giá bán</MenuItem>
              <MenuItem value="costPrice">Giá nhập</MenuItem>
              <MenuItem value="stockQuantity">Tồn kho</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            size="small"
            label="Giá trị"
            type={bulkEditField === 'sku' ? 'text' : 'number'}
            value={bulkEditValue}
            onChange={(e) => setBulkEditValue(e.target.value)}
            required={bulkEditField === 'sellingPrice' || bulkEditField === 'stockQuantity'}
            helperText={
              (bulkEditField === 'sellingPrice' || bulkEditField === 'stockQuantity') &&
              bulkEditValue === ''
                ? 'Trường này là bắt buộc và phải lớn hơn hoặc bằng 0'
                : 'Giá trị này sẽ được áp dụng cho tất cả biến thể'
            }
            error={
              (bulkEditField === 'sellingPrice' || bulkEditField === 'stockQuantity') &&
              bulkEditValue === ''
            }
          />

          <Button
            fullWidth
            variant="contained"
            onClick={onBulkEdit}
            disabled={!bulkEditField || bulkEditValue === ''}
          >
            Áp dụng
          </Button>
        </Stack>
      </Box>
    </Menu>
  );
}

BulkEditMenu.propTypes = {
  anchorEl: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  bulkEditField: PropTypes.string.isRequired,
  setBulkEditField: PropTypes.func.isRequired,
  bulkEditValue: PropTypes.string.isRequired,
  setBulkEditValue: PropTypes.func.isRequired,
  onBulkEdit: PropTypes.func.isRequired,
  isEditMode: PropTypes.bool,
};
