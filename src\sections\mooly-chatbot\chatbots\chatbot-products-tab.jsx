'use client';

import { useMemo, useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import { TextField } from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';
import InputAdornment from '@mui/material/InputAdornment';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import CircularProgress from '@mui/material/CircularProgress';
import Checkbox from '@mui/material/Checkbox';
import Toolbar from '@mui/material/Toolbar';

import { useChatbotProducts, removeProductsFromChatbot, invalidateChatbotProductsCache } from 'src/actions/mooly-chatbot/chatbot-product-service';
import { getBotTypeProductConfig } from 'src/actions/mooly-chatbot/chatbot-product-filter-service';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { EmptyContent } from 'src/components/empty-content';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { useAuthContext } from 'src/auth/hooks';

import BotTypeProductInfo from './components/bot-type-product-info';
import ChatbotProductAddDialog from './chatbot-product-add-dialog';

// ----------------------------------------------------------------------

export default function ChatbotProductsTab({ chatbot }) {
  const theme = useTheme();
  const { user } = useAuthContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);

  const addDialog = useBoolean();
  const removeDialog = useBoolean();
  const bulkRemoveDialog = useBoolean();

  // Lấy danh sách sản phẩm của chatbot sử dụng kiến trúc mới
  const {
    products,
    isLoading,
    error,
    mutate: refreshProducts,
  } = useChatbotProducts(chatbot?.id);

  // Xử lý khi thay đổi trang
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    setSelectedProducts([]); // Clear selection khi chuyển trang
  };

  // Xử lý khi thay đổi số hàng mỗi trang
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    setSelectedProducts([]); // Clear selection khi thay đổi rows per page
  };

  // Xử lý khi thay đổi truy vấn tìm kiếm
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
    setSelectedProducts([]); // Clear selection khi tìm kiếm
  };

  // Xử lý khi thêm sản phẩm vào chatbot
  const handleAddProductSuccess = () => {
    refreshProducts();
    invalidateChatbotProductsCache(chatbot?.id);
    addDialog.onFalse();
    toast.success('Đã thêm sản phẩm vào chatbot thành công');
  };

  // Xử lý khi mở dialog xóa sản phẩm đơn lẻ
  const handleOpenRemoveDialog = (productId) => {
    setSelectedProductId(productId);
    removeDialog.onTrue();
  };

  // Xử lý khi xóa sản phẩm đơn lẻ khỏi chatbot
  const handleRemoveProduct = async () => {
    if (!selectedProductId || !chatbot?.id) return;

    try {
      setIsUpdating(true);

      // Sử dụng service mới để xóa sản phẩm khỏi chatbot
      const result = await removeProductsFromChatbot(
        chatbot.id,
        [selectedProductId],
        user?.app_metadata?.tenant_id
      );

      refreshProducts();
      invalidateChatbotProductsCache(chatbot?.id);
      
      if (result.count > 0) {
        toast.success('Đã xóa sản phẩm khỏi chatbot thành công');
      } else {
        toast.info('Sản phẩm không tồn tại trong chatbot');
      }
    } catch (err) {
      console.error('Error removing product from chatbot:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi xóa sản phẩm khỏi chatbot');
    } finally {
      setIsUpdating(false);
      removeDialog.onFalse();
      setSelectedProductId(null);
    }
  };

  // Xử lý select/deselect một sản phẩm
  const handleSelectProduct = (productId) => {
    setSelectedProducts((prev) => {
      if (prev.includes(productId)) {
        return prev.filter((id) => id !== productId);
      }
      return [...prev, productId];
    });
  };

  // Xử lý select/deselect tất cả sản phẩm trên trang hiện tại
  const handleSelectAllProducts = (checked) => {
    if (checked) {
      const currentPageProductIds = paginatedProducts?.map((product) => product.id) || [];
      setSelectedProducts((prev) => {
        const newSelected = [...prev];
        currentPageProductIds.forEach((id) => {
          if (!newSelected.includes(id)) {
            newSelected.push(id);
          }
        });
        return newSelected;
      });
    } else {
      const currentPageProductIds = paginatedProducts?.map((product) => product.id) || [];
      setSelectedProducts((prev) => prev.filter((id) => !currentPageProductIds.includes(id)));
    }
  };

  // Xử lý xóa nhiều sản phẩm cùng lúc
  const handleBulkRemoveProducts = async () => {
    if (!selectedProducts.length || !chatbot?.id) return;

    try {
      setIsUpdating(true);

      // Sử dụng service để xóa nhiều sản phẩm cùng lúc
      const result = await removeProductsFromChatbot(
        chatbot.id,
        selectedProducts,
        user?.app_metadata?.tenant_id
      );

      refreshProducts();
      invalidateChatbotProductsCache(chatbot?.id);
      setSelectedProducts([]); // Clear selection
      
      if (result.count > 0) {
        toast.success(`Đã xóa ${result.count} sản phẩm khỏi chatbot thành công`);
      } else {
        toast.info('Không có sản phẩm nào được xóa');
      }
    } catch (err) {
      console.error('Error removing products from chatbot:', err);
      toast.error(err.message || 'Có lỗi xảy ra khi xóa sản phẩm khỏi chatbot');
    } finally {
      setIsUpdating(false);
      bulkRemoveDialog.onFalse();
    }
  };

  // Lọc sản phẩm theo từ khóa tìm kiếm
  const filteredProducts = products?.filter((product) => {
    const query = searchQuery.toLowerCase();
    return (
      product.name.toLowerCase().includes(query) ||
      (product.sku && product.sku.toLowerCase().includes(query)) ||
      (product.description && product.description.toLowerCase().includes(query))
    );
  });

  // Phân trang sản phẩm
  const paginatedProducts = filteredProducts?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Kiểm tra trạng thái select all cho trang hiện tại
  const currentPageProductIds = paginatedProducts?.map((product) => product.id) || [];
  const isAllCurrentPageSelected = currentPageProductIds.length > 0 && 
    currentPageProductIds.every((id) => selectedProducts.includes(id));
  const isSomeCurrentPageSelected = currentPageProductIds.some((id) => selectedProducts.includes(id));

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <Box sx={{ py: 5, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Hiển thị thông báo lỗi nếu có
  if (error) {
    return (
      <Box sx={{ py: 5, textAlign: 'center' }}>
        <Typography variant="body1" color="error">
          Có lỗi xảy ra khi tải dữ liệu sản phẩm
        </Typography>
      </Box>
    );
  }

  // Lấy cấu hình lọc sản phẩm theo bot type
  const botType = chatbot?.type || 'sale_bot';
  const productConfig = getBotTypeProductConfig(botType);

  return (
    <Stack spacing={3}>
      <Stack
        spacing={2}
        direction={{ xs: 'column', sm: 'row' }}
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        justifyContent="space-between"
      >
        <Stack spacing={1}>
          <Typography variant="h6">Sản phẩm được tư vấn bởi chatbot</Typography>
          <BotTypeProductInfo botType={botType} variant="description" />
        </Stack>

        <Button
          variant="contained"
          startIcon={<Iconify icon="eva:plus-fill" />}
          onClick={addDialog.onTrue}
        >
          Thêm sản phẩm
        </Button>
      </Stack>

      <Card>
        <Stack spacing={2} sx={{ p: 2 }}>
          <TextField
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Tìm kiếm sản phẩm..."
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
            }}
          />
        </Stack>

        {/* Bulk Actions Toolbar */}
        {selectedProducts.length > 0 && (
          <Toolbar
            sx={{
              pl: { sm: 2 },
              pr: { xs: 1, sm: 1 },
              ...(selectedProducts.length > 0 && {
                bgcolor: alpha(theme.palette.primary.main, 0.08),
              }),
            }}
          >
            <Typography
              sx={{ flex: '1 1 100%' }}
              color="inherit"
              variant="subtitle1"
              component="div"
            >
              Đã chọn {selectedProducts.length} sản phẩm
            </Typography>

            <Tooltip title="Xóa các sản phẩm đã chọn">
              <IconButton
                color="error"
                onClick={bulkRemoveDialog.onTrue}
                disabled={isUpdating}
              >
                <Iconify icon="eva:trash-2-outline" />
              </IconButton>
            </Tooltip>
          </Toolbar>
        )}

        {filteredProducts?.length === 0 ? (
          <EmptyContent
            title="Không có sản phẩm nào"
            description="Chưa có sản phẩm nào được thêm vào chatbot này"
            sx={{ py: 5 }}
          />
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        indeterminate={isSomeCurrentPageSelected && !isAllCurrentPageSelected}
                        checked={isAllCurrentPageSelected}
                        onChange={(event) => handleSelectAllProducts(event.target.checked)}
                        inputProps={{
                          'aria-label': 'Chọn tất cả sản phẩm trên trang này',
                        }}
                      />
                    </TableCell>
                    <TableCell>Sản phẩm</TableCell>
                    <TableCell>Mã SKU</TableCell>
                    <TableCell>Giá</TableCell>
                    <TableCell>Trạng thái</TableCell>
                    <TableCell align="right">Thao tác</TableCell>
                  </TableRow>
                </TableHead>

                <TableBody>
                  {paginatedProducts?.map((product) => {
                    const isSelected = selectedProducts.includes(product.id);
                    
                    return (
                      <TableRow 
                        key={product.id}
                        hover
                        onClick={() => handleSelectProduct(product.id)}
                        role="checkbox"
                        aria-checked={isSelected}
                        tabIndex={-1}
                        selected={isSelected}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={isSelected}
                            inputProps={{
                              'aria-labelledby': `enhanced-table-checkbox-${product.id}`,
                            }}
                          />
                        </TableCell>

                        <TableCell>
                          <Stack direction="row" alignItems="center" spacing={2}>
                            <Box
                              component="img"
                              src={product.avatar || '/assets/placeholder.svg'}
                              alt={product.name}
                              sx={{
                                width: 48,
                                height: 48,
                                borderRadius: 1,
                                objectFit: 'cover',
                                border: `solid 1px ${theme.palette.divider}`,
                              }}
                            />
                            <Typography variant="body2" noWrap>
                              {product.name}
                            </Typography>
                          </Stack>
                        </TableCell>

                        <TableCell>{product.sku || '-'}</TableCell>

                        <TableCell>
                          {new Intl.NumberFormat('vi-VN', {
                            style: 'currency',
                            currency: 'VND',
                          }).format(product.sellingPrice || product.selling_price || 0)}
                        </TableCell>

                        <TableCell>
                          <Box
                            sx={{
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              px: 1,
                              borderRadius: 1,
                              bgcolor: alpha(
                                theme.palette[product.status?.color || 'info'].main, 
                                0.1
                              ),
                              color: theme.palette[product.status?.color || 'info'].main,
                            }}
                          >
                            {product.status?.label || 
                              (product.isActive 
                                ? (product.trackInventory && product.stockQuantity <= 0 ? 'Hết hàng' : 'Đang bán')
                                : 'Ngừng bán'
                              )
                            }
                          </Box>
                        </TableCell>

                        <TableCell align="right">
                          <Tooltip title="Xóa khỏi chatbot">
                            <IconButton
                              color="error"
                              onClick={(e) => {
                                e.stopPropagation(); // Ngăn row selection
                                handleOpenRemoveDialog(product.id);
                              }}
                            >
                              <Iconify icon="eva:trash-2-outline" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={filteredProducts?.length || 0}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={[5, 10, 25]}
              labelRowsPerPage="Số hàng mỗi trang:"
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
            />
          </>
        )}
      </Card>

      {/* Dialog thêm sản phẩm */}
      <ChatbotProductAddDialog
        open={addDialog.value}
        onClose={addDialog.onFalse}
        chatbot={chatbot}
        onSuccess={handleAddProductSuccess}
      />

      {/* Dialog xác nhận xóa sản phẩm đơn lẻ */}
      <ConfirmDialog
        open={removeDialog.value}
        onClose={removeDialog.onFalse}
        title="Xóa sản phẩm khỏi chatbot"
        content="Bạn có chắc chắn muốn xóa sản phẩm này khỏi chatbot không?"
        action={
          <Button
            variant="contained"
            color="error"
            onClick={handleRemoveProduct}
            disabled={isUpdating}
          >
            {isUpdating ? <CircularProgress size={24} /> : 'Xóa'}
          </Button>
        }
      />

      {/* Dialog xác nhận xóa nhiều sản phẩm */}
      <ConfirmDialog
        open={bulkRemoveDialog.value}
        onClose={bulkRemoveDialog.onFalse}
        title="Xóa nhiều sản phẩm khỏi chatbot"
        content={
          <Stack spacing={2}>
            <Typography>
              Bạn có chắc chắn muốn xóa <strong>{selectedProducts.length}</strong> sản phẩm đã chọn khỏi chatbot không?
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Hành động này sẽ xóa sản phẩm khỏi cả Supabase và Weaviate.
            </Typography>
          </Stack>
        }
        action={
          <Button
            variant="contained"
            color="error"
            onClick={handleBulkRemoveProducts}
            disabled={isUpdating}
          >
            {isUpdating ? <CircularProgress size={24} /> : `Xóa ${selectedProducts.length} sản phẩm`}
          </Button>
        }
      />
    </Stack>
  );
}
