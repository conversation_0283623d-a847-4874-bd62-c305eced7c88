 
import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { fileData, FileThumbnail } from 'src/components/file-thumbnail';

/**
 * Component dialog chọn hình ảnh cho biến thể
 */
export default function ImageSelector({ open, media, onClose, onSelectImage, selectedImage }) {
  
  /**
   * Tối ưu: Hàm kiểm tra xem hình ảnh đã được chọn chưa
   * @param {string|File|Object} image - Hình ảnh cần kiểm tra
   * @returns {boolean} - True nếu đã được chọn
   */
  const isSelected = (image) => {
    if (!selectedImage) return false;

    try {
      // Case 1: Cả hai đều là URL strings
      if (typeof selectedImage === 'string' && typeof image === 'string') {
        return selectedImage === image;
      }

      // Case 2: selectedImage là URL, image là File object
      if (typeof selectedImage === 'string' && (image instanceof File || (image && image.name))) {
        // Không thể so sánh trực tiếp, return false
        return false;
      }

      // Case 3: selectedImage là File, image là URL
      if ((selectedImage instanceof File || (selectedImage && selectedImage.name)) && typeof image === 'string') {
        return false;
      }

      // Case 4: Cả hai đều là File objects
      if ((selectedImage instanceof File || (selectedImage && selectedImage.name)) &&
          (image instanceof File || (image && image.name))) {
        return selectedImage.name === image.name && selectedImage.size === image.size;
      }

      // Case 5: selectedImage là object có url property
      if (selectedImage.url) {
        if (typeof image === 'string') {
          return selectedImage.url === image;
        }
        if (image.url) {
          return selectedImage.url === image.url;
        }
      }

      return false;
    } catch (error) {
      console.error('Error comparing images:', error);
      return false;
    }
  };

  /**
   * Tối ưu: Lấy URL để hiển thị từ image object
   * @param {string|File|Object} image - Hình ảnh cần lấy URL
   * @returns {string} - URL để hiển thị
   */
  const getDisplayUrl = (image) => {
    try {
      // Case 1: Đã là URL string
      if (typeof image === 'string') {
        return image;
      }

      // Case 2: Object có url property
      if (image && typeof image === 'object' && image.url) {
        return image.url;
      }

      // Case 3: File object - tạo blob URL
      if (image instanceof File) {
        return URL.createObjectURL(image);
      }

      // Case 4: Object có thể là File data
      if (image && image.name && image.size) {
        return URL.createObjectURL(image);
      }

      return null;
    } catch (error) {
      console.error('Error getting display URL:', error);
      return null;
    }
  };

  /**
   * Tối ưu: Lấy tên file để hiển thị
   * @param {string|File|Object} image - Hình ảnh cần lấy tên
   * @returns {string} - Tên file
   */
  const getDisplayName = (image) => {
    try {
      // Case 1: File object
      if (image instanceof File) {
        return image.name;
      }

      // Case 2: Object có name property
      if (image && image.name) {
        return image.name;
      }

      // Case 3: URL string - lấy tên từ URL
      if (typeof image === 'string') {
        const urlParts = image.split('/');
        const fileName = urlParts[urlParts.length - 1];
        // Decode URI component để hiển thị đúng tên tiếng Việt
        return decodeURIComponent(fileName.split('?')[0]);
      }

      return 'Unknown';
    } catch (error) {
      console.error('Error getting display name:', error);
      return 'Unknown';
    }
  };

  /**
   * Tối ưu: Xử lý click chọn ảnh
   * @param {string|File|Object} image - Ảnh được chọn
   */
  const handleImageClick = (image) => {
    try {
      console.log('🖱️ ImageSelector - Image clicked:', image);
      console.log('🎯 ImageSelector - Image type:', typeof image);
      
      // Truyền nguyên vẹn image object/URL cho useVariantManager xử lý
      onSelectImage(image);
    } catch (error) {
      console.error('❌ Error handling image click:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Chọn ảnh cho biến thể</DialogTitle>
      <DialogContent>
        {Array.isArray(media) && media.length > 0 ? (
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {media.map((image, index) => {
              if (!image) {
                return null;
              }

              const displayUrl = getDisplayUrl(image);
              const displayName = getDisplayName(image);

              // Skip nếu không có URL để hiển thị
              if (!displayUrl) {
                return null;
              }

              return (
                <Grid item size={{ xs: 6, sm: 4, md: 3 }} key={index}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      height: 140,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.2s',
                      position: 'relative',
                      border: (theme) =>
                        isSelected(image) ? `2px solid ${theme.palette.primary.main}` : 'none',
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: 6,
                      },
                    }}
                    onClick={() => handleImageClick(image)}
                  >
                    <Stack
                      alignItems="center"
                      justifyContent="center"
                      sx={{ width: '100%', height: '100%', p: 1 }}
                    >
                      <FileThumbnail
                        file={displayUrl}
                        tooltip
                        imageView
                        sx={{ width: '100%', height: '100%', borderRadius: 1 }}
                        slotProps={{
                          img: {
                            sx: {
                              objectFit: 'contain',
                            },
                            onError: (e) => {
                              // Thay thế bằng hình ảnh mặc định khi lỗi
                              e.target.src = '/assets/placeholder.svg';
                            },
                          },
                        }}
                      />

                      {displayName && (
                        <Typography
                          variant="caption"
                          noWrap
                          sx={{
                            mt: 0.5,
                            maxWidth: '100%',
                            textAlign: 'center',
                          }}
                        >
                          {displayName}
                        </Typography>
                      )}

                      {isSelected(image) && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'primary.contrastText',
                            fontSize: 14,
                            fontWeight: 'bold',
                          }}
                        >
                          ✓
                        </Box>
                      )}
                    </Stack>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        ) : (
          <Box
            sx={{
              py: 6,
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'column',
              justifyContent: 'center',
            }}
          >
            <Typography variant="h6" sx={{ mb: 1, color: 'text.disabled' }}>
              Chưa có hình ảnh nào
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Vui lòng thêm hình ảnh cho sản phẩm trước khi chọn ảnh cho biến thể
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
}

ImageSelector.propTypes = {
  open: PropTypes.bool,
  media: PropTypes.array,
  onClose: PropTypes.func,
  onSelectImage: PropTypes.func,
  selectedImage: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
};
