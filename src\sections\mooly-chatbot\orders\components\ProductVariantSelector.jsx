'use client';

import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';

import { fCurrency } from 'src/utils/format-number';

import { getProductVariants } from 'src/actions/mooly-chatbot/product-variant-service';

import { getDisplayPrice, normalizeFieldData } from './orderFieldConfig';

/**
 * Component chọn biến thể sản phẩm
 */
export function ProductVariantSelector({ product, onVariantSelect, isVariantOutOfStock }) {
  const [variants, setVariants] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load variants khi component mount
  useEffect(() => {
    const loadVariants = async () => {
      if (!product?.id) return;

      setIsLoading(true);
      setError(null);

      try {
        const result = await getProductVariants(product.id);

        if (result.success && result.data) {
          const normalizedVariants = normalizeFieldData(result.data, 'variant');
          setVariants(normalizedVariants);
        } else {
          setError('Không thể tải biến thể');
          setVariants([]);
        }
      } catch (err) {
        console.error('Error loading variants:', err);
        setError('Lỗi khi tải biến thể');
        setVariants([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadVariants();
  }, [product?.id]);

  // Xử lý chọn biến thể
  const handleVariantClick = (variant) => {
    // Không cho phép chọn variant hết hàng
    if (isVariantOutOfStock && isVariantOutOfStock(variant)) {
      return;
    }
    onVariantSelect(product, variant);
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'background.neutral' }}>
        <CircularProgress size={20} />
        <Typography variant="caption" sx={{ ml: 1 }}>
          Đang tải biến thể...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'background.neutral' }}>
        <Typography variant="caption" color="error">
          {error}
        </Typography>
      </Box>
    );
  }

  if (!variants.length) {
    return (
      <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'background.neutral' }}>
        <Typography variant="caption" color="text.secondary">
          Không có biến thể nào
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ bgcolor: 'background.neutral' }}>
      {variants.map((variant) => (
        <VariantRow
          key={variant.id}
          variant={variant}
          onClick={() => handleVariantClick(variant)}
          isOutOfStock={isVariantOutOfStock ? isVariantOutOfStock(variant) : false}
        />
      ))}
    </Box>
  );
}

/**
 * Component hiển thị một dòng biến thể
 */
function VariantRow({ variant, onClick, isOutOfStock }) {
  const stockQuantity = variant.stockQuantity || 0;
  const handleClick = () => {
    if (!isOutOfStock) {
      onClick();
    }
  };

  return (
    <Stack
      direction="row"
      spacing={2}
      alignItems="center"
      sx={{
        p: 1.5,
        pl: 4, // Indent để phân biệt với sản phẩm chính
        borderBottom: 1,
        borderColor: 'divider',
        '&:hover': !isOutOfStock ? { bgcolor: 'action.hover' } : {},
        cursor: !isOutOfStock ? 'pointer' : 'default',
        bgcolor: isOutOfStock ? 'action.disabledBackground' : 'background.neutral',
        opacity: isOutOfStock ? 0.6 : 1
      }}
      onClick={handleClick}
    >
      {/* Checkbox placeholder để căn chỉnh */}
      <Box sx={{ width: 32 }} />

      <Stack direction="row" spacing={2} alignItems="center" sx={{ flex: 1 }}>
        {/* <Avatar
          src={variant.avatar}
          sx={{ width: 28, height: 28 }}
        >
          {variant.name?.charAt(0) || 'V'}
        </Avatar> */}

        <Box sx={{ flex: 1 }}>
          <Typography
            variant="body2"
            sx={{
              fontSize: '0.8rem',
              color: isOutOfStock ? 'text.disabled' : 'text.primary'
            }}
          >
            {variant.name || 'Biến thể'}
            {isOutOfStock && (
              <Typography
                component="span"
                variant="caption"
                sx={{
                  ml: 1,
                  color: 'error.main',
                  fontWeight: 'bold',
                  fontSize: '0.65rem'
                }}
              >
                (Hết hàng)
              </Typography>
            )}
          </Typography>
          {variant.attributes && Object.keys(variant.attributes).length > 0 && (
            <Typography
              variant="caption"
              color={isOutOfStock ? 'text.disabled' : 'text.secondary'}
              sx={{ fontSize: '0.7rem' }}
            >
              {Object.entries(variant.attributes)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ')}
            </Typography>
          )}
        </Box>
      </Stack>

      <Typography
        variant="body2"
        sx={{
          width: 120,
          textAlign: 'center',
          fontSize: '0.75rem',
          color: isOutOfStock ? 'text.disabled' : 'text.primary'
        }}
      >
        {variant.sku || '-'}
      </Typography>

      <Typography
        variant="body2"
        sx={{
          width: 120,
          textAlign: 'center',
          fontSize: '0.75rem',
          color: isOutOfStock ? 'text.disabled' : 'text.primary'
        }}
      >
        {fCurrency(getDisplayPrice(variant))}
      </Typography>

      <Typography
        variant="body2"
        sx={{
          width: 100,
          textAlign: 'center',
          fontSize: '0.75rem',
          color: isOutOfStock ? 'error.main' : (stockQuantity <= 5 ? 'warning.main' : 'text.primary'),
          fontWeight: isOutOfStock || stockQuantity <= 5 ? 'bold' : 'normal'
        }}
      >
        {stockQuantity}
        {stockQuantity <= 5 && stockQuantity > 0 && (
          <Typography
            component="span"
            variant="caption"
            sx={{
              ml: 0.5,
              color: 'warning.main',
              fontSize: '0.65rem'
            }}
          >
            (Sắp hết)
          </Typography>
        )}
      </Typography>
    </Stack>
  );
}

ProductVariantSelector.propTypes = {
  product: PropTypes.object.isRequired,
  onVariantSelect: PropTypes.func.isRequired,
  isVariantOutOfStock: PropTypes.func,
};

VariantRow.propTypes = {
  variant: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  isOutOfStock: PropTypes.bool,
};
